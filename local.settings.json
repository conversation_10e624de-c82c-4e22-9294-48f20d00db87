{"IsEncrypted": false, "Values": {"FUNCTIONS_WORKER_RUNTIME": "node", "AzureWebJobsStorage": "UseDevelopmentStorage=true", "COSMOS_DB_CONNECTIONSTRING": "AccountEndpoint=https://emr-dev-cosmosdb.documents.azure.com:443/;AccountKey=****************************************************************************************;", "#COSMOS_DB_CONNECTIONSTRING": "AccountEndpoint=https://localhost:8081/;AccountKey=****************************************************************************************", "COSMOS_DB_DATABASE": "ArcaAudioLayer", "OPENAI_ENDPOINT": "https://erm-dev-openai.openai.azure.com", "OPENAI_KEY": "********************************", "OPENAI_MODEL": "emrsummary4o", "CLIENT_ID": "f22ad9c9-3fe2-4921-b825-ed8b887f3ab7", "CLIENT_SECRET": "****************************************", "TENANT_ID": "cecfdadd-c501-4007-8619-84df7c41930b", "TENANT_NAME": "erm20240520", "signin_policy": "B2C_1_emrapp", "environment": "local", "cosmos_running_mode": "emulator", "SummaryInfo": "presentingcomplaint,historyofpresenting,pastmedicalhistory,pastsurgicalhistory,familyhistory,addictionhistory,diethistory,physicalactivityhistory,stresshistory,sleephistory,currentmedicationhistory", "REDISCACHEHOSTNAME": "emrdevcache.redis.cache.windows.net", "REDISCACHEKEY": "uCKqYP4eyambfCQG0HoxCnOP2eJUJ2pLRAzCaJQMsuM=", "EXPIRED_TIME": "600000", "AZURE_STORAGE_ACCOUNT_NAME": "ermdevstoragedata", "AZURE_STORAGE_ACCOUNT_KEY": "****************************************************************************************"}, "Host": {"CORS": "*"}}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arcaai EHR Application - Complete Architecture Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
        }
        .code-font {
            font-family: 'JetBrains Mono', monospace;
        }
        .architecture-diagram {
            font-family: 'JetBrains Mono', monospace;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 1rem;
            white-space: pre;
            overflow-x: auto;
        }
        @media print {
            body {
                font-size: 12px;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Header -->
    <header class="bg-blue-900 text-white py-8">
        <div class="container mx-auto px-6">
            <h1 class="text-4xl font-bold mb-2">
                <i class="fas fa-hospital-alt mr-3"></i>
                Arcaai EHR Application
            </h1>
            <p class="text-xl text-blue-100">Complete Architecture Documentation</p>
            <div class="mt-4 text-sm text-blue-200">
                <i class="fas fa-calendar mr-2"></i>
                Technical Documentation v1.0
            </div>
        </div>
    </header>

    <div class="container mx-auto px-6 py-8 max-w-7xl">
        <!-- Table of Contents -->
        <section class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-2xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-list mr-2 text-blue-600"></i>
                Table of Contents
            </h2>
            <div class="grid md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">1.</span>
                        <a href="#system-overview" class="text-blue-600 hover:underline">System Overview</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">2.</span>
                        <a href="#technology-stack" class="text-blue-600 hover:underline">Technology Stack</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">3.</span>
                        <a href="#architecture-patterns" class="text-blue-600 hover:underline">Architecture Patterns</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">4.</span>
                        <a href="#application-structure" class="text-blue-600 hover:underline">Application Structure</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">5.</span>
                        <a href="#data-layer" class="text-blue-600 hover:underline">Data Layer</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">6.</span>
                        <a href="#business-logic-layer" class="text-blue-600 hover:underline">Business Logic Layer</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">7.</span>
                        <a href="#api-layer" class="text-blue-600 hover:underline">API Layer</a>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">8.</span>
                        <a href="#security-authentication" class="text-blue-600 hover:underline">Security & Authentication</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">9.</span>
                        <a href="#external-integrations" class="text-blue-600 hover:underline">External Integrations</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">10.</span>
                        <a href="#development-environment" class="text-blue-600 hover:underline">Development Environment</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">11.</span>
                        <a href="#cicd-pipeline" class="text-blue-600 hover:underline">CI/CD Pipeline</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">12.</span>
                        <a href="#deployment-architecture" class="text-blue-600 hover:underline">Deployment Architecture</a>
                    </div>
                    <div class="flex items-center">
                        <span class="w-8 text-blue-600 font-semibold">13.</span>
                        <a href="#conclusion" class="text-blue-600 hover:underline">Conclusion</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- System Overview -->
        <section id="system-overview" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-sitemap mr-3 text-blue-600"></i>
                System Overview
            </h2>
            <p class="text-lg mb-6 text-gray-700">
                The Arcaai EHR (Electronic Health Records) application is a comprehensive healthcare management system built as a serverless microservice architecture on Microsoft Azure. The system manages patient records, doctor profiles, appointments, lab tests, prescriptions, and lifestyle data with integrated payment processing and ABDM (Ayushman Bharat Digital Mission) compliance.
            </p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-star mr-2 text-yellow-500"></i>
                Key Features
            </h3>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                    <h4 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-user-injured mr-2"></i>Patient Management
                    </h4>
                    <p class="text-sm text-blue-700">Complete patient lifecycle management with medical history</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                    <h4 class="font-semibold text-green-800 mb-2">
                        <i class="fas fa-user-md mr-2"></i>Doctor Portal
                    </h4>
                    <p class="text-sm text-green-700">Doctor profiles, EMR customization, and consultation management</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-500">
                    <h4 class="font-semibold text-purple-800 mb-2">
                        <i class="fas fa-calendar-check mr-2"></i>Appointment System
                    </h4>
                    <p class="text-sm text-purple-700">Scheduling and queue management</p>
                </div>
                <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                    <h4 class="font-semibold text-red-800 mb-2">
                        <i class="fas fa-flask mr-2"></i>Lab Management
                    </h4>
                    <p class="text-sm text-red-700">Test ordering, report processing with OCR capabilities</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                    <h4 class="font-semibold text-yellow-800 mb-2">
                        <i class="fas fa-prescription-bottle-alt mr-2"></i>Prescription System
                    </h4>
                    <p class="text-sm text-yellow-700">Medicine management and prescription packages</p>
                </div>
                <div class="bg-indigo-50 p-4 rounded-lg border-l-4 border-indigo-500">
                    <h4 class="font-semibold text-indigo-800 mb-2">
                        <i class="fas fa-credit-card mr-2"></i>Payment Integration
                    </h4>
                    <p class="text-sm text-indigo-700">Razorpay payment gateway integration</p>
                </div>
                <div class="bg-pink-50 p-4 rounded-lg border-l-4 border-pink-500">
                    <h4 class="font-semibold text-pink-800 mb-2">
                        <i class="fas fa-id-card mr-2"></i>ABDM Integration
                    </h4>
                    <p class="text-sm text-pink-700">ABHA number generation and verification</p>
                </div>
                <div class="bg-teal-50 p-4 rounded-lg border-l-4 border-teal-500">
                    <h4 class="font-semibold text-teal-800 mb-2">
                        <i class="fas fa-heartbeat mr-2"></i>Lifestyle Tracking
                    </h4>
                    <p class="text-sm text-teal-700">Patient lifestyle and nutrition monitoring</p>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg border-l-4 border-orange-500">
                    <h4 class="font-semibold text-orange-800 mb-2">
                        <i class="fas fa-robot mr-2"></i>AI-Powered
                    </h4>
                    <p class="text-sm text-orange-700">OpenAI integration for medical summaries and ambient listening</p>
                </div>
            </div>
        </section>

        <!-- Technology Stack -->
        <section id="technology-stack" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-layer-group mr-3 text-blue-600"></i>
                Technology Stack
            </h2>

            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-server mr-2 text-green-600"></i>Backend Framework
                    </h3>
                    <div class="space-y-2 mb-6">
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-20">Runtime:</span>
                            <span class="text-gray-600">Node.js 20</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 min-w-[80px]">Framework:</span>
                            <span class="text-gray-600">Azure Functions v4 (Serverless)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-20">Language:</span>
                            <span class="text-gray-600">JavaScript (ES6+)</span>
                        </div>
                    </div>

                    <h3 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-database mr-2 text-blue-600"></i>Database & Storage
                    </h3>
                    <div class="space-y-2 mb-6">
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-32">Primary Database:</span>
                            <span class="text-gray-600">Azure Cosmos DB (NoSQL)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-32">Caching:</span>
                            <span class="text-gray-600">Azure Redis Cache</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-32">File Storage:</span>
                            <span class="text-gray-600">Azure Blob Storage</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-32">Search:</span>
                            <span class="text-gray-600">Cosmos DB SQL API with custom indexing</span>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-cloud mr-2 text-blue-600"></i>Cloud Platform
                    </h3>
                    <div class="space-y-2 mb-6">
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-32">Platform:</span>
                            <span class="text-gray-600">Microsoft Azure</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-32">Compute:</span>
                            <span class="text-gray-600">Azure Functions (Consumption Plan)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-32">API Management:</span>
                            <span class="text-gray-600">Azure API Management (APIM)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-32">Container Registry:</span>
                            <span class="text-gray-600">Azure Container Registry</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-32">Identity:</span>
                            <span class="text-gray-600">Azure Active Directory B2C</span>
                        </div>
                    </div>

                    <h3 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-plug mr-2 text-purple-600"></i>External Services
                    </h3>
                    <div class="space-y-2 mb-6">
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-20">AI/ML:</span>
                            <span class="text-gray-600">Azure OpenAI Service (GPT-4)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-20">Payment:</span>
                            <span class="text-gray-600">Razorpay Payment Gateway</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-20">Email:</span>
                            <span class="text-gray-600">Custom SMTP service</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 w-20">OCR:</span>
                            <span class="text-gray-600">Custom OCR service for lab reports</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="font-medium text-gray-700 w-24">Standards:</span>
                            <span class="text-gray-600 flex-1">ICD-11, SNOMED CT, LOINC</span>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-tools mr-2 text-orange-600"></i>Development Tools
            </h3>
            <div class="grid md:grid-cols-4 gap-4">
                <div class="bg-gray-50 p-3 rounded">
                    <div class="font-medium text-gray-700">IDE</div>
                    <div class="text-sm text-gray-600">Visual Studio Code</div>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                    <div class="font-medium text-gray-700">Version Control</div>
                    <div class="text-sm text-gray-600">Git</div>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                    <div class="font-medium text-gray-700">Package Manager</div>
                    <div class="text-sm text-gray-600">npm</div>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                    <div class="font-medium text-gray-700">Containerization</div>
                    <div class="text-sm text-gray-600">Docker</div>
                </div>
            </div>
        </section>

        <!-- Architecture Patterns -->
        <section id="architecture-patterns" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-drafting-compass mr-3 text-blue-600"></i>
                Architecture Patterns
            </h2>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-layer-group mr-2 text-green-600"></i>1. Clean Architecture
            </h3>
            <p class="text-gray-700 mb-4">The application follows clean architecture principles with clear separation of concerns:</p>
            
            <div class="architecture-diagram text-sm">
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Azure Functions│  │   API Gateway   │  │  Middleware  │ │
│  │   (Controllers) │  │     (APIM)      │  │ (Auth/CORS)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │    Handlers     │  │    Services     │  │   Utilities  │ │
│  │  (Use Cases)    │  │ (Domain Logic)  │  │   (Helpers)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Data Access Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Repositories   │  │     Queries     │  │    Models    │ │
│  │(CRUD Operations)│  │ (sql queries)   │  │(Data Schema) │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Cosmos DB     │  │   Redis Cache   │  │ Blob Storage │ │
│  │   (Database)    │  │   (Caching)     │  │ (Files/Docs) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
            </div>

            <h3 class="text-xl font-semibold mb-4 mt-8 text-gray-800">
                <i class="fas fa-database mr-2 text-blue-600"></i>2. Repository Pattern
            </h3>
            <div class="grid md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">Repositories</h4>
                    <p class="text-sm text-blue-700">Handle all database operations and data persistence</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-green-800 mb-2">Queries</h4>
                    <p class="text-sm text-green-700">Contains SQL queries</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-purple-800 mb-2">Models</h4>
                    <p class="text-sm text-purple-700">Data schema definitions and validation</p>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-cogs mr-2 text-orange-600"></i>3. Service Layer Pattern
            </h3>
            <div class="grid md:grid-cols-3 gap-4">
                <div class="bg-orange-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-orange-800 mb-2">Services</h4>
                    <p class="text-sm text-orange-700">Business logic and domain operations</p>
                </div>
                <div class="bg-red-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-red-800 mb-2">Handlers</h4>
                    <p class="text-sm text-red-700">Request/response processing and validation</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-yellow-800 mb-2">Utilities</h4>
                    <p class="text-sm text-yellow-700">Shared helper functions and utilities</p>
                </div>
            </div>
        </section>

        <!-- Application Structure -->
        <section id="application-structure" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-folder-tree mr-3 text-blue-600"></i>
                Application Structure
            </h2>

            <div class="bg-gray-900 text-green-400 p-6 rounded-lg overflow-x-auto code-font text-sm">
<pre>src/
├── auth/                    # Authentication utilities
├── common/                  # Shared utilities and constants
│   ├── auth-message.js     # Authentication messages
│   ├── constant.js         # Application constants
│   ├── helper.js           # Common helper functions
│   ├── permissions.js      # Permission definitions
│   ├── roles.js           # Role definitions
│   └── user-validation.js  # User validation logic
├── cosmosDbContext/        # Database context and configuration
│   └── comosdb-context.js  # Cosmos DB client and utilities
├── functions/              # Azure Functions (API endpoints)
│   ├── index.js           # Main function app configuration
│   ├── auth.js            # Authentication endpoints
│   ├── patient.js         # Patient management endpoints
│   ├── doctor.js          # Doctor management endpoints
│   ├── appointment.js     # Appointment endpoints
│   ├── payment.js         # Payment endpoints
│   ├── abdm.js            # ABDM integration endpoints
│   └── [other-functions]  # Additional domain-specific endpoints
├── handlers/               # Business logic handlers
│   ├── patient-handler.js  # Patient business logic
│   ├── doctor-handler.js   # Doctor business logic
│   ├── payment-handler.js  # Payment processing logic
│   └── [other-handlers]    # Additional handlers
├── services/               # Domain services
│   ├── patient-service.js  # Patient domain service
│   ├── doctor-service.js   # Doctor domain service
│   ├── payment-service.js  # Payment service
│   ├── openai-service.js   # AI integration service
│   ├── b2c-service.js      # Azure B2C integration
│   └── [other-services]    # Additional services
├── repositories/           # Data access layer
│   ├── patient-repository.js    # Patient data operations
│   ├── doctor-repository.js     # Doctor data operations
│   ├── payment-repository.js    # Payment data operations
│   └── [other-repositories]     # Additional repositories
├── queries/                # Optimized read operations
│   ├── patient-query.js    # Patient query operations
│   ├── medicine-query.js   # Medicine query operations
│   └── [other-queries]     # Additional queries
├── models/                 # Data models and schemas
│   ├── patient-model.js    # Patient data model
│   ├── doctor-model.js     # Doctor data model
│   └── [other-models]      # Additional models
├── utils/                  # Utility functions
│   ├── pagination.js       # Pagination utilities
│   ├── sanitization.js     # Data sanitization
│   └── [other-utils]       # Additional utilities
└── tasks/                  # Background tasks and cron jobs
    ├── finalize-patient-history-cron.js
    └── finalize-records-cron.js</pre>
            </div>
        </section>

        <!-- Data Layer -->
        <section id="data-layer" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-database mr-3 text-blue-600"></i>
                Data Layer
            </h2>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-atom mr-2 text-purple-600"></i>Cosmos DB Architecture
            </h3>

            <div class="grid md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">Database Configuration</h4>
                    <div class="space-y-2">
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-24">Database:</span>
                            <div class="text-gray-600">
                                <div>Development: ArcaAudioLayer</div>
                                <div>Production: emr</div>
                            </div>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-24">API:</span>
                            <span class="text-gray-600">NoSQL API (Document-based)</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-24">Consistency:</span>
                            <span class="text-gray-600">Session (default)</span>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">Throughput Configuration</h4>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="font-medium text-gray-700 min-w-[96px]">Development:</span>
                            <span class="text-gray-600">100-1000 RU/s</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-24">Production:</span>
                            <span class="text-gray-600">400-4000 RU/s</span>
                        </div>
                    </div>
                </div>
            </div>

            <h4 class="text-lg font-semibold mb-3 text-gray-800">Container Strategy</h4>
            <p class="text-gray-700 mb-4">Each domain has dedicated containers with optimized partition keys:</p>

            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-6">
<pre>// Container Examples
{
  "patients": { partitionKey: "/id", throughput: 400 },
  "doctors": { partitionKey: "/id", throughput: 400 },
  "appointments": { partitionKey: "/doctorId", throughput: 400 },
  "medicines": { partitionKey: "/id", throughput: 400 },
  "lab_tests": { partitionKey: "/patientId", throughput: 400 },
  "payments": { partitionKey: "/organizationId", throughput: 400 },
  "organizations": { partitionKey: "/id", throughput: 400 },
  "users": { partitionKey: "/organizationId", throughput: 400 }
}</pre>
            </div>

            <h4 class="text-lg font-semibold mb-3 text-gray-800">Performance Optimization</h4>
            <div class="grid md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-blue-800 mb-2">Batch Operations</h5>
                    <p class="text-sm text-blue-700">Partition-aware batching (max 250 operations per partition)</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-green-800 mb-2">Connection Pooling</h5>
                    <p class="text-sm text-green-700">Shared Cosmos DB client with connection reuse</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-purple-800 mb-2">Caching</h5>
                    <p class="text-sm text-purple-700">Redis cache for frequently accessed data</p>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-memory mr-2 text-red-600"></i>Redis Cache Strategy
            </h3>
            <div class="grid md:grid-cols-3 gap-4">
                <div class="bg-red-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-red-800 mb-2">Session Storage</h5>
                    <p class="text-sm text-red-700">User sessions and JWT tokens</p>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-orange-800 mb-2">Frequently Accessed Data</h5>
                    <p class="text-sm text-orange-700">Medicine lists, lab test catalogs</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-yellow-800 mb-2">Temporary Data</h5>
                    <p class="text-sm text-yellow-700">OTP codes</p>
                </div>
            </div>
        </section>

        <!-- Business Logic Layer -->
        <section id="business-logic-layer" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-brain mr-3 text-blue-600"></i>
                Business Logic Layer
            </h2>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-tasks mr-2 text-green-600"></i>Handler Pattern
            </h3>
            <p class="text-gray-700 mb-4">Each domain has handlers that process business logic:</p>
            <div class="grid md:grid-cols-2 gap-4 mb-6">
                <div class="bg-green-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-green-800 mb-2">Patient Handler</h4>
                    <p class="text-sm text-green-700">Patient lifecycle management</p>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">Doctor Handler</h4>
                    <p class="text-sm text-blue-700">Doctor management and EMR customization</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-purple-800 mb-2">Appointment Handler</h4>
                    <p class="text-sm text-purple-700">Appointment scheduling and management</p>
                </div>
                <div class="bg-red-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-red-800 mb-2">Payment Handler</h4>
                    <p class="text-sm text-red-700">Razorpay payment processing</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-yellow-800 mb-2">ABDM Handler</h4>
                    <p class="text-sm text-yellow-700">ABHA number management</p>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-cogs mr-2 text-blue-600"></i>Service Pattern
            </h3>
            <p class="text-gray-700 mb-4">Services contain domain-specific business logic:</p>
            <div class="grid md:grid-cols-2 gap-4 mb-6">
                <div class="bg-indigo-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-indigo-800 mb-2">Patient Service</h4>
                    <p class="text-sm text-indigo-700">Core patient operations</p>
                </div>
                <div class="bg-pink-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-pink-800 mb-2">Doctor Service</h4>
                    <p class="text-sm text-pink-700">Doctor profile management</p>
                </div>
                <div class="bg-teal-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-teal-800 mb-2">Payment Service</h4>
                    <p class="text-sm text-teal-700">Payment processing logic</p>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-orange-800 mb-2">B2C Service</h4>
                    <p class="text-sm text-orange-700">Azure B2C user management</p>
                </div>
                <div class="bg-cyan-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-cyan-800 mb-2">Email Service</h4>
                    <p class="text-sm text-cyan-700">Email notifications</p>
                </div>
                <div class="bg-lime-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-lime-800 mb-2">OpenAI Service</h4>
                    <p class="text-sm text-lime-700">AI-powered medical summaries</p>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-plug mr-2 text-purple-600"></i>External Service Integrations
            </h3>

            <div class="space-y-6">
                <div class="border-l-4 border-blue-500 pl-4">
                    <h4 class="text-lg font-semibold text-gray-800 mb-2">OpenAI Service</h4>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-3 rounded">
                            <div class="font-medium text-blue-800">Medical Summaries</div>
                            <div class="text-sm text-blue-700">Conversation analysis and summary generation</div>
                        </div>
                        <div class="bg-blue-50 p-3 rounded">
                            <div class="font-medium text-blue-800">Ambient Listening</div>
                            <div class="text-sm text-blue-700">Real-time conversation processing</div>
                        </div>
                        <div class="bg-blue-50 p-3 rounded">
                            <div class="font-medium text-blue-800">Lifestyle Analysis</div>
                            <div class="text-sm text-blue-700">Patient lifestyle data interpretation</div>
                        </div>
                    </div>
                </div>

                <div class="border-l-4 border-green-500 pl-4">
                    <h4 class="text-lg font-semibold text-gray-800 mb-2">B2C Service</h4>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-green-50 p-3 rounded">
                            <div class="font-medium text-green-800">User Management</div>
                            <div class="text-sm text-green-700">Azure AD B2C integration</div>
                        </div>
                        <div class="bg-green-50 p-3 rounded">
                            <div class="font-medium text-green-800">Authentication</div>
                            <div class="text-sm text-green-700">JWT token validation</div>
                        </div>
                        <div class="bg-green-50 p-3 rounded">
                            <div class="font-medium text-green-800">User Provisioning</div>
                            <div class="text-sm text-green-700">Automated user creation and management</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- API Layer -->
        <section id="api-layer" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-network-wired mr-3 text-blue-600"></i>
                API Layer
            </h2>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-bolt mr-2 text-yellow-600"></i>Azure Functions Configuration
            </h3>
            
            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-6">
<pre>// Function App Settings
{
  "version": "2.0",
  "extensionBundle": {
    "id": "Microsoft.Azure.Functions.ExtensionBundle",
    "version": "[4.*, 5.0.0)"
  },
  "logging": {
    "applicationInsights": {
      "samplingSettings": {
        "isEnabled": true,
        "excludedTypes": "Request"
      }
    }
  }
}</pre>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-gateway mr-2 text-blue-600"></i>API Gateway (APIM) Configuration
            </h3>
            
            <div class="mb-6">
                <h4 class="text-lg font-semibold mb-3 text-gray-800">Base URLs:</h4>
                <div class="space-y-2">
                    <div class="bg-gray-50 p-3 rounded border-l-4 border-blue-500">
                        <div class="font-medium text-gray-800">Main API</div>
                        <div class="text-sm text-gray-600 code-font">https://emr-ms-dev-apim.azure-api.net/EMR-MS/api/v0.1</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded border-l-4 border-green-500">
                        <div class="font-medium text-gray-800">ABDM</div>
                        <div class="text-sm text-gray-600 code-font">https://emr-ms-dev-apim.azure-api.net/abdm/v0.1</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded border-l-4 border-purple-500">
                        <div class="font-medium text-gray-800">Appointments</div>
                        <div class="text-sm text-gray-600 code-font">https://emr-ms-dev-apim.azure-api.net/appointment/v0.1</div>
                    </div>
                </div>
            </div>

            <div class="grid md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-blue-800 mb-2">CORS</h5>
                    <p class="text-sm text-blue-700">Configured for cross-origin requests</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-green-800 mb-2">Authentication</h5>
                    <p class="text-sm text-green-700">JWT Bearer token validation</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-purple-800 mb-2">Rate Limiting</h5>
                    <p class="text-sm text-purple-700">API rate limiting and throttling</p>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-route mr-2 text-orange-600"></i>Function Routing Strategy
            </h3>

            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm">
<pre>// Example function definition
app.http('patient-profile', {
  methods: ['GET', 'POST', 'PATCH'],
  route: 'patient',
  authLevel: 'function',
  handler: async (req, context) => {
    return await patientHandler.handleRequest(req)
  }
})</pre>
            </div>
        </section>

        <!-- Security & Authentication -->
        <section id="security-authentication" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-shield-alt mr-3 text-blue-600"></i>
                Security & Authentication
            </h2>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-key mr-2 text-green-600"></i>Authentication Architecture
            </h3>

            <div class="architecture-diagram text-sm mb-6">
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   Azure APIM    │───▶│ Azure Functions │
│                 │    │  (API Gateway)  │    │   (Backend)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Azure AD B2C   │    │  JWT Validation │    │ Role-Based Auth │
│ (Identity Mgmt) │    │   & Rate Limit  │    │  & Permissions  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-certificate mr-2 text-blue-600"></i>JWT Token Structure
            </h3>

            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-6">
<pre>// Actual Azure B2C JWT Token Structure
{
  "exp": 1755690404,
  "nbf": 1755689804,
  "ver": "1.0",
  "iss": "https://erm20240520.b2clogin.com/cecfdadd-c501-4007-8619-84df7c41930b/v2.0/",
  "sub": "80fa2c02-96ae-4524-ab82-1f3832936928",
  "aud": "f22ad9c9-3fe2-4921-b825-ed8b887f3ab7",
  "nonce": "************",
  "iat": 1755689804,
  "auth_time": 1755689799,
  "oid": "80fa************",
  "name": "James",
  "emails": ["<EMAIL>"],
  "tfp": "B2C_1_emrapp"
}</pre>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-users-cog mr-2 text-purple-600"></i>Role-Based Access Control (RBAC)
            </h3>

            <div class="grid md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">Default Roles</h4>
                    <div class="space-y-2">
                        <div class="bg-red-50 p-2 rounded border-l-4 border-red-500">
                            <div class="font-medium text-red-800">Super Admin</div>
                        </div>
                        <div class="bg-orange-50 p-2 rounded border-l-4 border-orange-500">
                            <div class="font-medium text-orange-800">Organization Super Admin</div>
                        </div>
                        <div class="bg-yellow-50 p-2 rounded border-l-4 border-yellow-500">
                            <div class="font-medium text-yellow-800">Organization Admin</div>
                        </div>
                        <div class="bg-green-50 p-2 rounded border-l-4 border-green-500">
                            <div class="font-medium text-green-800">Doctor</div>
                        </div>
                        <div class="bg-blue-50 p-2 rounded border-l-4 border-blue-500">
                            <div class="font-medium text-blue-800">Nurse</div>
                        </div>
                        <div class="bg-purple-50 p-2 rounded border-l-4 border-purple-500">
                            <div class="font-medium text-purple-800">Receptionist</div>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">Sample Permissions (50+ available)</h4>
                    <div class="space-y-2">
                        <div class="bg-gray-50 p-2 rounded text-sm">
                            <div class="font-medium">emr.access</div>
                            <div class="text-gray-600">Access to EMR system</div>
                        </div>
                        <div class="bg-gray-50 p-2 rounded text-sm">
                            <div class="font-medium">patient.view</div>
                            <div class="text-gray-600">View patient information</div>
                        </div>
                        <div class="bg-gray-50 p-2 rounded text-sm">
                            <div class="font-medium">patient.manage</div>
                            <div class="text-gray-600">Manage patient records</div>
                        </div>
                        <div class="bg-gray-50 p-2 rounded text-sm">
                            <div class="font-medium">appointment.manage</div>
                            <div class="text-gray-600">Manage appointments</div>
                        </div>
                        <div class="bg-gray-50 p-2 rounded text-sm">
                            <div class="font-medium">prescription.create</div>
                            <div class="text-gray-600">Create prescriptions</div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-lock mr-2 text-red-600"></i>Data Security
            </h3>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-red-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-red-800 mb-2">Encryption at Rest</h5>
                    <p class="text-sm text-red-700">Cosmos DB automatic encryption (built-in)</p>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-orange-800 mb-2">Encryption in Transit</h5>
                    <p class="text-sm text-orange-700">HTTPS/TLS 1.2+ for all communications</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-yellow-800 mb-2">Data Sanitization</h5>
                    <p class="text-sm text-yellow-700">Input validation and sanitization</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-green-800 mb-2">PII Protection</h5>
                    <p class="text-sm text-green-700">Patient data encryption and access logging</p>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-blue-800 mb-2">Audit Logging</h5>
                    <p class="text-sm text-blue-700">Comprehensive audit trail for operations</p>
                </div>
            </div>
        </section>

        <!-- External Integrations -->
        <section id="external-integrations" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-plug mr-3 text-blue-600"></i>
                External Integrations
            </h2>

            <div class="space-y-8">
                <!-- ABDM Integration -->
                <div class="border-l-4 border-green-500 pl-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-id-card mr-2 text-green-600"></i>1. ABDM (Ayushman Bharat Digital Mission)
                    </h3>
                    <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-4">
<pre>// ABDM Service Configuration
{
  baseUrl: 'https://abhasbx.abdm.gov.in/abha/api/v3',
  clientId: '********',
  clientSecret: '**********',
  operations: [
    'initiate-aadhaar',
    'initiate-mobile',
    'verify-otp',
    'details-by-number'
  ]
}</pre>
                    </div>
                </div>

                <!-- Razorpay Integration -->
                <div class="border-l-4 border-blue-500 pl-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-credit-card mr-2 text-blue-600"></i>2. Razorpay Payment Gateway
                    </h3>
                    <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-4">
<pre>// Payment Configuration
{
  keyId: 'r**********************',
  keySecret: 'rzp_test_********************',
  webhookSecret: 'webhook_secret',
  paymentTypes: [
    'patient_registration',
    'consultation',
    'prescription',
    'lab_tests'
  ]
}</pre>
                    </div>
                </div>

                <!-- OpenAI Integration -->
                <div class="border-l-4 border-purple-500 pl-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-robot mr-2 text-purple-600"></i>3. Azure OpenAI Service
                    </h3>
                    <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-4">
<pre>// OpenAI Configuration
{
  endpoint: 'https://erm-dev-openai.openai.azure.com',
  apiKey: '******************',
  model: 'emrsummary4o',
  features: [
    'medical_summaries',
    'ambient_listening',
    'lifestyle_analysis'
  ]
}</pre>
                    </div>
                </div>

                <!-- Healthcare Standards -->
                <div class="border-l-4 border-red-500 pl-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-stethoscope mr-2 text-red-600"></i>4. Healthcare Standards Integration
                    </h3>
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <div class="bg-red-50 p-4 rounded-lg text-center">
                            <h4 class="font-semibold text-red-800 mb-2">ICD-11</h4>
                            <p class="text-sm text-red-700">International Classification of Diseases</p>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg text-center">
                            <h4 class="font-semibold text-orange-800 mb-2">SNOMED CT</h4>
                            <p class="text-sm text-orange-700">Systematized Nomenclature of Medicine Clinical Terms</p>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg text-center">
                            <h4 class="font-semibold text-yellow-800 mb-2">LOINC</h4>
                            <p class="text-sm text-yellow-700">Logical Observation Identifiers Names and Codes</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg text-center">
                            <h4 class="font-semibold text-green-800 mb-2">HL7 FHIR</h4>
                            <p class="text-sm text-green-700">Healthcare data exchange standards (planned)</p>
                        </div>
                    </div>
                </div>

                <!-- OCR Service -->
                <div class="border-l-4 border-yellow-500 pl-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800">
                        <i class="fas fa-file-pdf mr-2 text-yellow-600"></i>5. OCR Service Integration
                    </h3>
                    <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm">
<pre>// OCR Service for Lab Reports
{
  endpoint: 'http://ocrcontainergroup-v1.eastus.azurecontainer.io:8000/ocr/',
  supportedFormats: ['PDF'], // Only PDF supported currently
  features: [
    'text_extraction',
    'structured_data_parsing',
    'medical_terminology_recognition'
  ]
}</pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Development Environment -->
        <section id="development-environment" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-code mr-3 text-blue-600"></i>
                Development Environment
            </h2>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-laptop-code mr-2 text-green-600"></i>Local Development Setup
            </h3>

            <div class="grid md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">Prerequisites</h4>
                    <div class="space-y-2">
                        <div class="bg-gray-50 p-3 rounded border-l-4 border-blue-500">
                            <div class="font-medium text-gray-800">Node.js 20+</div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded border-l-4 border-green-500">
                            <div class="font-medium text-gray-800">Azure Functions Core Tools v4</div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded border-l-4 border-purple-500">
                            <div class="font-medium text-gray-800">Azure CLI</div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded border-l-4 border-red-500">
                            <div class="font-medium text-gray-800">Visual Studio Code</div>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">Environment Configuration</h4>
                    <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm">
<pre>{
  "FUNCTIONS_WORKER_RUNTIME": "node",
  "COSMOS_DB_CONNECTIONSTRING": "local_cosmos_connection",
  "COSMOS_DB_DATABASE": "ArcaAudioLayer",
  "OPENAI_ENDPOINT": "azure_openai_endpoint",
  "CLIENT_ID": "azure_b2c_client_id",
  "TENANT_ID": "azure_tenant_id",
  "cosmos_running_mode": "emulator"
}</pre>
                    </div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-box mr-2 text-orange-600"></i>Package Dependencies
            </h3>

            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm">
<pre>{
  "dependencies": {
    "@azure/cosmos": "^4.0.0",
    "@azure/functions": "^4.5.0",
    "@azure/identity": "^4.3.0",
    "@azure/storage-blob": "^12.26.0",
    "@microsoft/microsoft-graph-client": "^3.0.7",
    "axios": "^1.7.2",
    "jsonwebtoken": "^9.0.2",
    "razorpay": "^2.9.6",
    "redis": "^4.7.0",
    "openai": "^4.38.5"
  }
}</pre>
            </div>
        </section>

        <!-- CI/CD Pipeline -->
        <section id="cicd-pipeline" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-rocket mr-3 text-blue-600"></i>
                CI/CD Pipeline
            </h2>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-tools mr-2 text-blue-600"></i>Azure DevOps Pipeline Configuration
            </h3>

            <h4 class="text-lg font-semibold mb-3 text-gray-800">Build Pipeline (azure-pipelines.yml)</h4>
            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-6">
<pre>trigger:
  - main
  - develop
  - test
pool:
  vmImage: 'ubuntu-latest'
    
steps:
- script: echo Hello, world!
  displayName: 'Run a one-line script'
    
- script: |
    echo Add other tasks to build, test, and deploy your project.
    echo See https://aka.ms/yaml
  displayName: 'Run a multi-line script'
    
- task: Docker@2
  inputs:
    containerRegistry: 'ermdevcontainer'
    repository: 'emr-v01/emr-ms'
    command: 'buildAndPush'
    Dockerfile: 'Dockerfile'
    tags: '$(Build.SourceBranchName).$(Build.BuildId)'
    
- task: PublishBuildArtifacts@1
  inputs:
    ArtifactName: 'emr-ms'
    publishLocation: 'Container'
    PathtoPublish: '.'</pre>
            </div>

            <h4 class="text-lg font-semibold mb-3 text-gray-800">Dockerfile Configuration</h4>
            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-6">
<pre>FROM mcr.microsoft.com/azure-functions/node:4-nightly-node20

ENV AzureWebJobsScriptRoot=/home/<USER>/wwwroot \
    AzureFunctionsJobHost__Logging__Console__IsEnabled=true

COPY . /home/<USER>/wwwroot

RUN cd /home/<USER>/wwwroot && \
    npm install</pre>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-cloud-upload-alt mr-2 text-green-600"></i>Release Pipeline Strategy
            </h3>

            <div class="grid md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">Development Environment</h4>
                    <div class="space-y-2">
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Environment:</span>
                            <span class="text-gray-600">dev</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Resource Group:</span>
                            <span class="text-gray-600">EMR-DEV</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Function App:</span>
                            <span class="text-gray-600">EMR-MS</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Cosmos DB:</span>
                            <span class="text-gray-600">emr-dev-cosmosdb</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Storage:</span>
                            <span class="text-gray-600">ermdevstoragedata</span>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-3 text-gray-800">Production Environment</h4>
                    <div class="space-y-2">
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Environment:</span>
                            <span class="text-gray-600">production</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Resource Group:</span>
                            <span class="text-gray-600">EMR-TEST</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Function App:</span>
                            <span class="text-gray-600">EMR-MS-TEST</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Cosmos DB:</span>
                            <span class="text-gray-600">emrdbtest</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-gray-700 w-32">Storage:</span>
                            <span class="text-gray-600">ermdevstoragedata</span>
                        </div>
                    </div>
                </div>
            </div>

            <h4 class="text-lg font-semibold mb-3 text-gray-800">Deployment Process</h4>
            <div class="space-y-3">
                <div class="flex items-center bg-blue-50 p-3 rounded-lg">
                    <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3">1</div>
                    <div>
                        <div class="font-medium text-blue-800">Build Trigger</div>
                        <div class="text-sm text-blue-700">Code commit to main/develop/test branches</div>
                    </div>
                </div>
                <div class="flex items-center bg-green-50 p-3 rounded-lg">
                    <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-bold mr-3">2</div>
                    <div>
                        <div class="font-medium text-green-800">Docker Build</div>
                        <div class="text-sm text-green-700">Create containerized application image</div>
                    </div>
                </div>
                <div class="flex items-center bg-purple-50 p-3 rounded-lg">
                    <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold mr-3">3</div>
                    <div>
                        <div class="font-medium text-purple-800">Image Push</div>
                        <div class="text-sm text-purple-700">Push to Azure Container Registry</div>
                    </div>
                </div>
                <div class="flex items-center bg-red-50 p-3 rounded-lg">
                    <div class="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center font-bold mr-3">4</div>
                    <div>
                        <div class="font-medium text-red-800">Release Trigger</div>
                        <div class="text-sm text-red-700">Manual deployment trigger</div>
                    </div>
                </div>
                <div class="flex items-center bg-yellow-50 p-3 rounded-lg">
                    <div class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center font-bold mr-3">5</div>
                    <div>
                        <div class="font-medium text-yellow-800">Environment Selection</div>
                        <div class="text-sm text-yellow-700">Choose target environment (DEV/TEST)</div>
                    </div>
                </div>
                <div class="flex items-center bg-indigo-50 p-3 rounded-lg">
                    <div class="w-8 h-8 bg-indigo-500 text-white rounded-full flex items-center justify-center font-bold mr-3">6</div>
                    <div>
                        <div class="font-medium text-indigo-800">Configuration Update</div>
                        <div class="text-sm text-indigo-700">Environment-specific settings</div>
                    </div>
                </div>
                <div class="flex items-center bg-teal-50 p-3 rounded-lg">
                    <div class="w-8 h-8 bg-teal-500 text-white rounded-full flex items-center justify-center font-bold mr-3">7</div>
                    <div>
                        <div class="font-medium text-teal-800">Health Check</div>
                        <div class="text-sm text-teal-700">Verify deployment success</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Deployment Architecture -->
        <section id="deployment-architecture" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-cloud mr-3 text-blue-600"></i>
                Deployment Architecture
            </h2>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-server mr-2 text-green-600"></i>Azure Infrastructure
            </h3>

            <h4 class="text-lg font-semibold mb-3 text-gray-800">Resource Groups</h4>
            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-6">
<pre>EMR-DEV/
├── EMR-MS (Function App)
├── emr-dev-cosmosdb (Cosmos DB Account)
├── EMR-MS-DEV-apim (API Management)
├── ermdevstoragedata (Storage Account)
├── emrdevcache (Redis Cache)
├── ermdevcontainer (Container Registry)</pre>
            </div>

            <h4 class="text-lg font-semibold mb-3 text-gray-800">Network Architecture</h4>
            <div class="architecture-diagram text-sm mb-6">
┌─────────────────────────────────────────────────────────────┐
│                    Internet Gateway                         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Azure API Management                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Rate Limiting │  │  Authentication │  │     CORS     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Azure Functions                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Patient APIs   │  │  Doctor APIs    │  │ Payment APIs │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  Data Layer                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Cosmos DB     │  │   Redis Cache   │  │ Blob Storage │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-expand-arrows-alt mr-2 text-blue-600"></i>Scaling Configuration
            </h3>

            <div class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto code-font text-sm mb-6">
<pre>// Cosmos DB Scaling
{
  "throughput": {
    "development": "100-1000 RU/s",
    "production": "400-4000 RU/s"
  },
  "autoscale": true,
  "maxThroughput": 4000
}</pre>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-shield-alt mr-2 text-purple-600"></i>Backup Strategy
            </h3>

            <div class="bg-purple-50 p-4 rounded-lg">
                <h4 class="font-semibold text-purple-800 mb-2">Cosmos DB Backup</h4>
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                        <div class="flex">
                            <span class="font-medium text-purple-700 w-32">Frequency:</span>
                            <span class="text-purple-600">Every 20-24 hours</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-purple-700 w-32">Restore:</span>
                            <span class="text-purple-600">Point-in-time restore</span>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex">
                            <span class="font-medium text-purple-700 w-32">Storage:</span>
                            <span class="text-purple-600">Geo-redundant backup</span>
                        </div>
                        <div class="flex">
                            <span class="font-medium text-purple-700 w-32">Retention:</span>
                            <span class="text-purple-600">30-day retention period</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Conclusion -->
        <section id="conclusion" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 border-b pb-3">
                <i class="fas fa-flag-checkered mr-3 text-blue-600"></i>
                Conclusion
            </h2>

            <p class="text-lg mb-6 text-gray-700">
                This EMR application is built as a modular monolith using Azure Functions, providing a scalable and maintainable healthcare management system. The architecture emphasizes clean separation of concerns, robust security, and efficient data management while maintaining the flexibility to evolve into microservices as needed.
            </p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">
                <i class="fas fa-trophy mr-2 text-yellow-600"></i>Key Architectural Strengths
            </h3>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                        <h4 class="font-semibold text-blue-800 mb-2">
                            <i class="fas fa-cubes mr-2"></i>Modular Monolith
                        </h4>
                        <p class="text-sm text-blue-700">Single deployment with clear domain separation</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                        <h4 class="font-semibold text-green-800 mb-2">
                            <i class="fas fa-bolt mr-2"></i>Serverless Architecture
                        </h4>
                        <p class="text-sm text-green-700">Cost-effective and automatically scalable Azure Functions</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-500">
                        <h4 class="font-semibold text-purple-800 mb-2">
                            <i class="fas fa-layer-group mr-2"></i>Clean Architecture
                        </h4>
                        <p class="text-sm text-purple-700">Maintainable codebase with separation of concerns</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                        <h4 class="font-semibold text-red-800 mb-2">
                            <i class="fas fa-shield-alt mr-2"></i>Security-First
                        </h4>
                        <p class="text-sm text-red-700">Comprehensive security with Azure B2C and role-based permissions</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                        <h4 class="font-semibold text-yellow-800 mb-2">
                            <i class="fas fa-stethoscope mr-2"></i>Healthcare Standards
                        </h4>
                        <p class="text-sm text-yellow-700">Integration with ICD-11, SNOMED CT, LOINC, and ABDM</p>
                    </div>
                    <div class="bg-indigo-50 p-4 rounded-lg border-l-4 border-indigo-500">
                        <h4 class="font-semibold text-indigo-800 mb-2">
                            <i class="fas fa-robot mr-2"></i>AI-Powered
                        </h4>
                        <p class="text-sm text-indigo-700">Advanced AI capabilities for medical insights and ambient listening</p>
                    </div>
                    <div class="bg-teal-50 p-4 rounded-lg border-l-4 border-teal-500">
                        <h4 class="font-semibold text-teal-800 mb-2">
                            <i class="fas fa-rocket mr-2"></i>DevOps Ready
                        </h4>
                        <p class="text-sm text-teal-700">Automated CI/CD pipeline with Docker containerization</p>
                    </div>
                </div>
            </div>

            <div class="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <p class="text-center text-lg text-gray-700 font-medium">
                    <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>
                    This architecture provides a solid foundation for a modern, scalable, and secure healthcare management system that can adapt to future requirements and technological advances.
                </p>
            </div>
        </section>
    </div>

    <script>
        document.getElementById('current-date').textContent = new Date().toLocaleDateString();
    </script>
</body>
</html>
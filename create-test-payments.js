const axios = require('axios');

async function createTestPayments() {
  const baseUrl = 'http://localhost:7071/api/payments/create-order';
  const organizationId = '43cac26c-c3fb-45e7-bfa2-dd59db9f13fd';

  console.log('=== CREATING TEST PAYMENTS ===');

  for (let i = 1; i <= 10; i++) {
    try {
      const paymentData = {
        amount: 100 + i, // Different amounts
        paymentType: 'consultation',
        patientId: `TEST_PATIENT_${i}`,
        organizationId: organizationId,
        description: `Test Payment ${i}`,
        metadata: {
          testPayment: true,
          sequence: i
        }
      };

      const response = await axios.post(baseUrl, paymentData, {
        headers: {
          'Authorization': 'Bearer dummy-token-for-local-testing',
          'Content-Type': 'application/json'
        }
      });

      console.log(`Payment ${i} created:`, response.data.data?.paymentId);
    } catch (error) {
      console.error(`Error creating payment ${i}:`, error.response?.data || error.message);
    }
  }

  console.log('=== TEST PAYMENTS CREATION COMPLETE ===');
}

// Run the test
createTestPayments();
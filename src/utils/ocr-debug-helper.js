const { LabTestStatus } = require('../common/constant')

/**
 * Debug helper for OCR processing issues
 * This utility helps diagnose why OCR data is not matching with lab tests
 */
class OCRDebugHelper {
  /**
   * Analyze OCR data and lab test matching
   * @param {Object} ocrData - The OCR data structure
   * @param {Array} labTests - Array of lab test objects
   * @param {Object} context - Azure Function context for logging
   */
  static analyzeOCRMatching(ocrData, labTests, context) {
    context.log('=== OCR DEBUG ANALYSIS ===')
    
    // Analyze OCR data structure
    context.log('OCR Data Structure:')
    context.log('- Full OCR Data:', JSON.stringify(ocrData, null, 2))
    
    const ocrTestResults = ocrData.structured?.test_results || []
    context.log(`- Found ${ocrTestResults.length} OCR test results`)
    
    if (ocrTestResults.length === 0) {
      context.log('❌ No test results found in OCR data')
      return {
        success: false,
        reason: 'No test results in OCR data',
        ocrData,
        labTests
      }
    }

    // Analyze each OCR test result
    ocrTestResults.forEach((ocrTest, index) => {
      context.log(`\nOCR Test ${index + 1}:`)
      context.log('- Raw OCR Test:', JSON.stringify(ocrTest, null, 2))
      
      // Check all possible field names for test name
      const possibleTestNames = [
        ocrTest['Test Name'],
        ocrTest.test_name,
        ocrTest.testName,
        ocrTest.name
      ].filter(Boolean)
      
      context.log('- Possible test names:', possibleTestNames)
      
      // Check all possible field names for result
      const possibleResults = [
        ocrTest.result,
        ocrTest.value,
        ocrTest.Value,
        ocrTest.Result
      ].filter(Boolean)
      
      context.log('- Possible results:', possibleResults)
      
      // Check all possible field names for reference
      const possibleReferences = [
        ocrTest.reference_interval,
        ocrTest['Reference Interval'],
        ocrTest.referenceInterval,
        ocrTest.reference,
        ocrTest.Reference
      ].filter(Boolean)
      
      context.log('- Possible references:', possibleReferences)
      
      // Check units
      const possibleUnits = [
        ocrTest.units,
        ocrTest.unit
      ].filter(Boolean)
      
      context.log('- Possible units:', possibleUnits)
    })

    // Analyze lab test structure
    context.log('\n=== LAB TESTS ANALYSIS ===')
    context.log(`Found ${labTests.length} lab tests to match`)
    
    labTests.forEach((test, index) => {
      context.log(`\nLab Test ${index + 1}:`)
      context.log('- Test Name:', test.testName)
      context.log('- Test ID:', test.testId)
      context.log('- Current Status:', test.status)
      context.log('- Current Results:', test.results)
      context.log('- Current Reference:', test.reference)
    })

    // Perform matching analysis
    context.log('\n=== MATCHING ANALYSIS ===')
    const matchingResults = this.performMatchingAnalysis(ocrTestResults, labTests, context)
    
    return {
      success: true,
      ocrTestCount: ocrTestResults.length,
      labTestCount: labTests.length,
      matchingResults,
      ocrData,
      labTests
    }
  }

  /**
   * Perform detailed matching analysis
   */
  static performMatchingAnalysis(ocrTestResults, labTests, context) {
    const results = []
    
    labTests.forEach((labTest, labIndex) => {
      context.log(`\nAnalyzing matches for Lab Test ${labIndex + 1}: "${labTest.testName}"`)
      
      const testName = labTest.testName?.toLowerCase().trim() || ''
      const matches = []
      
      ocrTestResults.forEach((ocrTest, ocrIndex) => {
        // Extract OCR test name with multiple field name variations
        let ocrTestName = ''
        if (ocrTest['Test Name']) {
          ocrTestName = ocrTest['Test Name'].toLowerCase().trim()
        } else if (ocrTest.test_name) {
          ocrTestName = ocrTest.test_name.toLowerCase().trim()
        } else if (ocrTest.testName) {
          ocrTestName = ocrTest.testName.toLowerCase().trim()
        } else if (ocrTest.name) {
          ocrTestName = ocrTest.name.toLowerCase().trim()
        }

        if (!ocrTestName) {
          context.log(`  OCR Test ${ocrIndex + 1}: No test name found`)
          return
        }

        context.log(`  OCR Test ${ocrIndex + 1}: "${ocrTestName}"`)

        // Check exact match
        if (testName === ocrTestName) {
          matches.push({
            ocrIndex,
            matchType: 'exact',
            similarity: 1.0,
            ocrTestName,
            ocrTest
          })
          context.log(`    ✓ EXACT MATCH`)
          return
        }

        // Check normalized match
        const normalizeString = (str) => {
          return str
            .replace(/[|]/g, 'i')
            .replace(/[1]/g, 'i')
            .replace(/[0]/g, 'o')
            .replace(/[-_]/g, ' ')
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s]/g, '')
            .trim()
        }

        const normalizedTestName = normalizeString(testName)
        const normalizedOCRName = normalizeString(ocrTestName)

        if (normalizedTestName === normalizedOCRName) {
          matches.push({
            ocrIndex,
            matchType: 'normalized',
            similarity: 1.0,
            ocrTestName,
            ocrTest
          })
          context.log(`    ✓ NORMALIZED MATCH`)
          return
        }

        // Check partial match
        if (testName.length > 5 && ocrTestName.length > 5) {
          if (normalizedTestName.includes(normalizedOCRName) || 
              normalizedOCRName.includes(normalizedTestName)) {
            matches.push({
              ocrIndex,
              matchType: 'partial',
              similarity: 0.9,
              ocrTestName,
              ocrTest
            })
            context.log(`    ✓ PARTIAL MATCH`)
            return
          }
        }

        // Check similarity
        if (testName.length > 8 && ocrTestName.length > 8) {
          const similarity = this.calculateStringSimilarity(normalizedTestName, normalizedOCRName)
          if (similarity >= 0.8) {
            matches.push({
              ocrIndex,
              matchType: 'similarity',
              similarity,
              ocrTestName,
              ocrTest
            })
            context.log(`    ✓ SIMILARITY MATCH (${similarity.toFixed(2)})`)
            return
          } else {
            context.log(`    ✗ Low similarity (${similarity.toFixed(2)})`)
          }
        } else {
          context.log(`    ✗ No match`)
        }
      })

      results.push({
        labTest,
        labIndex,
        matches,
        bestMatch: matches.length > 0 ? matches[0] : null
      })

      if (matches.length === 0) {
        context.log(`  ❌ NO MATCHES FOUND for "${labTest.testName}"`)
      } else {
        context.log(`  ✅ Found ${matches.length} potential matches`)
      }
    })

    return results
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  static calculateStringSimilarity(str1, str2) {
    const len1 = str1.length
    const len2 = str2.length

    if (len1 === 0) return len2 === 0 ? 1 : 0
    if (len2 === 0) return 0

    const matrix = Array(len1 + 1)
      .fill()
      .map(() => Array(len2 + 1).fill(0))

    for (let i = 0; i <= len1; i++) matrix[i][0] = i
    for (let j = 0; j <= len2; j++) matrix[0][j] = j

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        )
      }
    }

    const maxLen = Math.max(len1, len2)
    return (maxLen - matrix[len1][len2]) / maxLen
  }

  /**
   * Generate recommendations for fixing OCR matching issues
   */
  static generateRecommendations(analysisResult, context) {
    context.log('\n=== RECOMMENDATIONS ===')
    
    if (!analysisResult.success) {
      context.log('❌ OCR analysis failed:', analysisResult.reason)
      return
    }

    const { matchingResults, ocrTestCount, labTestCount } = analysisResult
    
    const totalMatches = matchingResults.filter(r => r.bestMatch).length
    const unmatchedLabTests = matchingResults.filter(r => !r.bestMatch)
    
    context.log(`📊 Summary:`)
    context.log(`- OCR Tests: ${ocrTestCount}`)
    context.log(`- Lab Tests: ${labTestCount}`)
    context.log(`- Successful Matches: ${totalMatches}`)
    context.log(`- Unmatched Lab Tests: ${unmatchedLabTests.length}`)

    if (unmatchedLabTests.length > 0) {
      context.log('\n🔍 Unmatched Lab Tests:')
      unmatchedLabTests.forEach(result => {
        context.log(`- "${result.labTest.testName}"`)
      })
      
      context.log('\n💡 Recommendations:')
      context.log('1. Check if lab test names match the OCR output exactly')
      context.log('2. Consider updating test names in the system to match OCR format')
      context.log('3. Verify OCR service is extracting test names correctly')
      context.log('4. Check for typos or formatting differences')
    }

    if (totalMatches === labTestCount) {
      context.log('✅ All lab tests have potential matches!')
    }
  }
}

module.exports = OCRDebugHelper

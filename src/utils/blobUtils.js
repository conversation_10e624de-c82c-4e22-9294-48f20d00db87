const {
  BlobServiceClient,
  StorageSharedKeyCredential,
  BlobSASPermissions,
  generateBlobSASQueryParameters,
} = require('@azure/storage-blob')

const connectionString = process.env.AzureWebJobsStorage || ''
const blobServiceClient =
  BlobServiceClient.fromConnectionString(connectionString)

// Create shared key credential for SAS generation
const sharedKeyCredential = new StorageSharedKeyCredential(
  process.env.AZURE_STORAGE_ACCOUNT_NAME,
  process.env.AZURE_STORAGE_ACCOUNT_KEY,
)

/**
 * Get a container client.
 */
const getContainerClient = (containerName) =>
  blobServiceClient.getContainerClient(containerName)

/**
 * Find a blob by prefix.
 */
const findBlobByPrefix = async (containerName, prefix) => {
  try {
    if (!containerName || !prefix) {
      throw new Error('Container name and prefix are required')
    }

    const containerClient = getContainerClient(containerName)

    // Check if container exists
    const containerExists = await containerClient.exists()
    if (!containerExists) {
      console.log(`Container ${containerName} does not exist`)
      return null
    }

    const blobs = containerClient.listBlobsFlat({ prefix })
    for await (const blob of blobs) {
      return blob.name
    }
    return null
  } catch (error) {
    console.error(
      `Error finding blob with prefix ${prefix} in container ${containerName}:`,
      error.message,
    )
    throw error
  }
}

/**
 * Delete blobs by prefix.
 */
const deleteBlobsByPrefix = async (containerName, prefix) => {
  try {
    if (!containerName || !prefix) {
      throw new Error('Container name and prefix are required')
    }

    const containerClient = getContainerClient(containerName)

    // Check if container exists
    const containerExists = await containerClient.exists()
    if (!containerExists) {
      console.log(
        `Container ${containerName} does not exist, nothing to delete`,
      )
      return
    }

    const blobs = containerClient.listBlobsFlat({ prefix })
    let deletedCount = 0

    for await (const blob of blobs) {
      const blockBlobClient = containerClient.getBlockBlobClient(blob.name)
      const deleteResult = await blockBlobClient.deleteIfExists()
      if (deleteResult.succeeded) {
        deletedCount++
      }
    }

    console.log(
      `Deleted ${deletedCount} blobs with prefix ${prefix} from container ${containerName}`,
    )
  } catch (error) {
    console.error(
      `Error deleting blobs with prefix ${prefix} from container ${containerName}:`,
      error.message,
    )
    throw error
  }
}

/**
 * Generate SAS URL for a blob with read permissions
 */
const generateBlobSASUrl = (containerName, blobName, expiryHours = 24) => {
  try {
    const sasOptions = {
      containerName,
      blobName,
      permissions: BlobSASPermissions.parse('r'), // Read permission
      startsOn: new Date(),
      expiresOn: new Date(new Date().valueOf() + expiryHours * 60 * 60 * 1000), // Default 24 hours
    }

    const sasToken = generateBlobSASQueryParameters(
      sasOptions,
      sharedKeyCredential,
    ).toString()
    return `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/${containerName}/${blobName}?${sasToken}`
  } catch (error) {
    console.error('Error generating SAS URL:', error.message)
    throw error
  }
}

/**
 * Upload a blob.
 */
const uploadBlob = async (containerName, blobName, buffer) => {
  const containerClient = getContainerClient(containerName)
  // Create container without public access (private by default)
  await containerClient.createIfNotExists()
  const blockBlobClient = containerClient.getBlockBlobClient(blobName)
  await blockBlobClient.uploadData(buffer)

  // Return SAS URL instead of direct blob URL for secure access
  return generateBlobSASUrl(containerName, blobName)
}

module.exports = {
  getContainerClient,
  findBlobByPrefix,
  deleteBlobsByPrefix,
  uploadBlob,
  generateBlobSASUrl,
}

// Age-based vital sign ranges
const VITAL_RANGES = {
  'Newborn (0–1 mo)': {
    height: { min: 45, max: 55 },
    weight: { min: 2.5, max: 4.5 },
    bp_systolic: { min: 60, max: 90 },
    bp_diastolic: { min: 20, max: 60 },
    pulse: { min: 100, max: 180 },
    rr: { min: 30, max: 60 },
    spO2: { min: 95, max: 100 },
    temperature: { min: 36.1, max: 37.2 }, // Celsius
  },
  'Infant (1–12 mo)': {
    height: { min: 60, max: 76 },
    weight: { min: 4, max: 10 },
    bp_systolic: { min: 70, max: 100 },
    bp_diastolic: { min: 50, max: 65 },
    pulse: { min: 100, max: 160 },
    rr: { min: 30, max: 60 },
    spO2: { min: 95, max: 100 },
    temperature: { min: 36.1, max: 37.2 },
  },
  '<PERSON><PERSON> (1–3 yrs)': {
    height: { min: 75, max: 95 },
    weight: { min: 10, max: 14 },
    bp_systolic: { min: 80, max: 110 },
    bp_diastolic: { min: 50, max: 80 },
    bmi: { min: 14, max: 17 },
    pulse: { min: 90, max: 150 },
    rr: { min: 24, max: 40 },
    spO2: { min: 95, max: 100 },
    temperature: { min: 36.1, max: 37.2 },
  },
  'Preschool (3–5 yrs)': {
    height: { min: 95, max: 110 },
    weight: { min: 13, max: 18 },
    bp_systolic: { min: 90, max: 110 },
    bp_diastolic: { min: 55, max: 80 },
    bmi: { min: 14, max: 17 },
    pulse: { min: 80, max: 140 },
    rr: { min: 22, max: 34 },
    spO2: { min: 95, max: 100 },
    temperature: { min: 36.1, max: 37.2 },
  },
  'School-age (6–12)': {
    height: { min: 115, max: 145 },
    weight: { min: 20, max: 40 },
    bp_systolic: { min: 95, max: 120 },
    bp_diastolic: { min: 60, max: 80 },
    bmi: { min: 15, max: 18.5 },
    pulse: { min: 75, max: 120 },
    rr: { min: 18, max: 30 },
    spO2: { min: 95, max: 100 },
    temperature: { min: 36.1, max: 37.2 },
  },
  'Teen (13–18 yrs)': {
    height: { min: 150, max: 175 },
    weight: { min: 40, max: 70 },
    bp_systolic: { min: 100, max: 120 },
    bp_diastolic: { min: 60, max: 80 },
    bmi: { min: 16, max: 24 },
    pulse: { min: 60, max: 100 },
    rr: { min: 12, max: 20 },
    spO2: { min: 95, max: 100 },
    temperature: { min: 36.1, max: 37.2 },
  },
  'Young Adult (19–25)': {
    height: { min: 150, max: 180 },
    weight: { min: 50, max: 85 },
    bp_systolic: { min: 100, max: 120 },
    bp_diastolic: { min: 60, max: 80 },
    bmi: { min: 18.5, max: 24.9 },
    pulse: { min: 60, max: 100 },
    rr: { min: 12, max: 20 },
    spO2: { min: 95, max: 100 },
    temperature: { min: 36.1, max: 37.2 },
  },
  'Adult (26–59 yrs)': {
    height: { min: 150, max: 180 },
    weight: { min: 50, max: 90 },
    bp_systolic: { min: 0, max: 120 },
    bp_diastolic: { min: 0, max: 80 },
    bmi: { min: 18.5, max: 24.9 },
    pulse: { min: 60, max: 100 },
    rr: { min: 12, max: 20 },
    spO2: { min: 95, max: 100 },
    temperature: { min: 36.1, max: 37.2 },
  },
  'Older Adult (60+)': {
    height: { min: 145, max: 175 },
    weight: { min: 45, max: 80 },
    bp_systolic: { min: 120, max: 140 },
    bp_diastolic: { min: 70, max: 90 },
    bmi: { min: 18.5, max: 24.9 },
    pulse: { min: 60, max: 100 },
    rr: { min: 12, max: 25 },
    spO2: { min: 95, max: 100 },
    temperature: { min: 36.1, max: 37.2 },
  },
}

// Vital sign status levels
const VITAL_STATUS = {
  NORMAL: 'normal',
  LOW: 'low',
  HIGH: 'high',
  UNKNOWN: 'unknown',
  INVALID: 'invalid',
}

/**
 * Calculate age from date of birth
 * @param {string|Date} dob - Date of birth
 * @returns {number|null} Age in years (with decimal precision)
 */
function calculateAge(dob) {
  if (!dob) return null
  const birthDate = new Date(dob)
  const today = new Date()

  // Calculate precise age in years (including decimal places)
  const ageInMilliseconds = today.getTime() - birthDate.getTime()
  const ageInYears = ageInMilliseconds / (365.25 * 24 * 60 * 60 * 1000)

  return ageInYears
}

/**
 * Get age group based on age for vital sign ranges
 * @param {number} age - Age in years
 * @returns {string|null} Age group category
 */
function getAgeGroup(age) {
  if (age === null || age === undefined) return null

  // Calculate age in months for more precise infant/newborn classification
  const ageInMonths = age * 12

  if (ageInMonths < 1) return 'Newborn (0–1 mo)'
  if (age < 1) return 'Infant (1–12 mo)'
  if (age < 3) return 'Toddler (1–3 yrs)'
  if (age < 6) return 'Preschool (3–5 yrs)'
  if (age < 13) return 'School-age (6–12)'
  if (age < 19) return 'Teen (13–18 yrs)'
  if (age < 26) return 'Young Adult (19–25)'
  if (age < 60) return 'Adult (26–59 yrs)'
  return 'Older Adult (60+)'
}

/**
 * Parse blood pressure string into systolic and diastolic values
 * @param {string} bpString - Blood pressure string (e.g., "120/80")
 * @returns {Object} Object with systolic and diastolic values
 */
function parseBP(bpString) {
  if (!bpString || typeof bpString !== 'string')
    return { systolic: null, diastolic: null }

  const parts = bpString.split('/')
  if (parts.length !== 2) return { systolic: null, diastolic: null }

  const systolic = parseFloat(parts[0])
  const diastolic = parseFloat(parts[1])

  return {
    systolic: isNaN(systolic) ? null : systolic,
    diastolic: isNaN(diastolic) ? null : diastolic,
  }
}

/**
 * Convert temperature from Fahrenheit to Celsius if needed
 * @param {number|string} temperature - Temperature value
 * @returns {number|null} Temperature in Celsius
 */
function normalizeTemperature(temperature) {
  if (
    !temperature ||
    temperature === '' ||
    temperature === null ||
    temperature === undefined
  ) {
    return null
  }

  const tempValue = parseFloat(temperature)
  if (isNaN(tempValue)) return null

  // If temperature is likely in Fahrenheit (> 50), convert to Celsius
  if (tempValue > 50) {
    return ((tempValue - 32) * 5) / 9
  }

  return tempValue
}

/**
 * Determine vital sign status based on value and normal range
 * @param {number|string} value - Vital sign value
 * @param {Object} range - Range object with min and max properties
 * @returns {string} Vital status (normal, low, high, unknown, invalid)
 */
function getVitalStatus(value, range) {
  if (
    !value ||
    !range ||
    value === '' ||
    value === null ||
    value === undefined
  ) {
    return VITAL_STATUS.UNKNOWN
  }

  const numValue = parseFloat(value)
  if (isNaN(numValue)) return VITAL_STATUS.INVALID

  if (numValue >= range.min && numValue <= range.max) {
    return VITAL_STATUS.NORMAL
  } else if (numValue < range.min) {
    return VITAL_STATUS.LOW
  } else {
    return VITAL_STATUS.HIGH
  }
}

/**
 * Evaluate all vital signs and assign status for each
 * @param {Object} vitals - Vital signs object
 * @param {string} ageGroup - Age group for range lookup
 * @returns {Object} Object with status for each vital sign
 */
function evaluateVitalSigns(vitals, ageGroup) {
  if (!ageGroup || !VITAL_RANGES[ageGroup]) {
    return {
      height: VITAL_STATUS.UNKNOWN,
      weight: VITAL_STATUS.UNKNOWN,
      bmi: VITAL_STATUS.UNKNOWN,
      pulse: VITAL_STATUS.UNKNOWN,
      bp: VITAL_STATUS.UNKNOWN,
      sbp: VITAL_STATUS.UNKNOWN,
      dbp: VITAL_STATUS.UNKNOWN,
      rr: VITAL_STATUS.UNKNOWN,
      spO2: VITAL_STATUS.UNKNOWN,
      temperature: VITAL_STATUS.UNKNOWN,
    }
  }

  const ranges = VITAL_RANGES[ageGroup]
  const statuses = {}

  // Evaluate each vital sign
  statuses.height = getVitalStatus(vitals.height, ranges.height)
  statuses.weight = getVitalStatus(vitals.weight, ranges.weight)
  statuses.bmi = getVitalStatus(vitals.bmi, ranges.bmi)
  statuses.pulse = getVitalStatus(vitals.pulse, ranges.pulse)
  statuses.rr = getVitalStatus(vitals.rr, ranges.rr)
  statuses.spO2 = getVitalStatus(vitals.spO2, ranges.spO2)

  // Handle temperature with automatic Fahrenheit to Celsius conversion
  const normalizedTemp = normalizeTemperature(vitals.temperature)
  statuses.temperature = getVitalStatus(normalizedTemp, ranges.temperature)

  // Handle blood pressure - support multiple formats
  let systolicValue = null
  let diastolicValue = null

  // Priority 1: Check for new sbp/dbp format
  if (vitals.sbp && vitals.dbp) {
    systolicValue = parseFloat(vitals.sbp)
    diastolicValue = parseFloat(vitals.dbp)
  }
  // Priority 2: Check for legacy systolic/diastolic format
  else if (vitals.systolic && vitals.diastolic) {
    systolicValue = parseFloat(vitals.systolic)
    diastolicValue = parseFloat(vitals.diastolic)
  }
  // Priority 3: Check for string format like "120/80"
  else if (vitals.bp || vitals.bloodPressure) {
    const bpString = vitals.bp || vitals.bloodPressure
    if (bpString && typeof bpString === 'string') {
      const { systolic, diastolic } = parseBP(bpString)
      systolicValue = systolic
      diastolicValue = diastolic
    }
  }

  // Evaluate individual BP components
  statuses.sbp = getVitalStatus(systolicValue, ranges.bp_systolic)
  statuses.dbp = getVitalStatus(diastolicValue, ranges.bp_diastolic)

  // Evaluate overall BP status (for backward compatibility)
  if (
    systolicValue !== null &&
    diastolicValue !== null &&
    !isNaN(systolicValue) &&
    !isNaN(diastolicValue)
  ) {
    const systolicStatus = statuses.sbp
    const diastolicStatus = statuses.dbp

    // BP status is determined by the worse of systolic or diastolic
    const statusPriority = {
      [VITAL_STATUS.NORMAL]: 0,
      [VITAL_STATUS.LOW]: 2,
      [VITAL_STATUS.HIGH]: 2,
      [VITAL_STATUS.UNKNOWN]: 1,
      [VITAL_STATUS.INVALID]: 3,
    }

    const systolicPriority = statusPriority[systolicStatus] || 1
    const diastolicPriority = statusPriority[diastolicStatus] || 1

    // Use the status with higher priority (worse condition)
    if (systolicPriority > diastolicPriority) {
      statuses.bp = systolicStatus
    } else if (diastolicPriority > systolicPriority) {
      statuses.bp = diastolicStatus
    } else {
      // If both have same priority, prefer abnormal over normal
      if (systolicStatus !== VITAL_STATUS.NORMAL) {
        statuses.bp = systolicStatus
      } else {
        statuses.bp = diastolicStatus
      }
    }
  } else {
    statuses.bp = VITAL_STATUS.UNKNOWN
  }

  return statuses
}

module.exports = {
  VITAL_RANGES,
  VITAL_STATUS,
  calculateAge,
  getAgeGroup,
  parseBP,
  normalizeTemperature,
  getVitalStatus,
  evaluateVitalSigns,
}

const { HttpResponse } = require('@azure/functions')
const crypto = require('crypto')
// Secret key will be loaded from ENCRYPTION_SECRET_KEY env (or default value if not set)
function getSecretKeyFromEnvOrDefault() {
  const envKey = process.env.ENCRYPTION_SECRET_KEY
  if (!envKey) {
    return Buffer.from('a1b2c3d4e5f67890a1b2c3d4e5f67890', 'utf8') // default (32 chars)
  }

  // If hex (64 chars), parse as hex
  if (/^[0-9a-fA-F]{64}$/.test(envKey)) {
    return Buffer.from(envKey, 'hex')
  }

  // Try base64
  try {
    const b = Buffer.from(envKey, 'base64')
    if (b.length === 32) return b
  } catch (e) {
    // ignore
  }

  // Otherwise use utf8, pad/truncate to 32 bytes
  const utf8 = Buffer.from(envKey, 'utf8')
  if (utf8.length === 32) return utf8
  if (utf8.length < 32) {
    const padded = Buffer.alloc(32)
    utf8.copy(padded)
    return padded
  }
  return utf8.slice(0, 32)
}
const secretKey = getSecretKeyFromEnvOrDefault()
const iv = Buffer.alloc(16, 0) // Fixed 16-byte IV for deterministic encryption

const helper = {
  parseJSON: (jsonString) => {
    try {
      if (typeof jsonString == 'object') {
        return jsonString
      }

      // Clean up the JSON string to handle common issues
      let cleanedString = jsonString.trim()

      // Remove any markdown code block markers
      cleanedString = cleanedString
        .replace(/^```json\s*/, '')
        .replace(/\s*```$/, '')

      // Try to fix truncated JSON by finding the last complete object/array
      if (!cleanedString.endsWith('}') && !cleanedString.endsWith(']')) {
        // Find the last complete JSON structure
        let lastCompleteIndex = -1
        let braceCount = 0
        let bracketCount = 0
        let inString = false
        let escapeNext = false

        for (let i = 0; i < cleanedString.length; i++) {
          const char = cleanedString[i]

          if (escapeNext) {
            escapeNext = false
            continue
          }

          if (char === '\\') {
            escapeNext = true
            continue
          }

          if (char === '"' && !escapeNext) {
            inString = !inString
            continue
          }

          if (!inString) {
            if (char === '{') braceCount++
            else if (char === '}') {
              braceCount--
              if (braceCount === 0 && bracketCount === 0) {
                lastCompleteIndex = i
              }
            } else if (char === '[') bracketCount++
            else if (char === ']') {
              bracketCount--
              if (braceCount === 0 && bracketCount === 0) {
                lastCompleteIndex = i
              }
            }
          }
        }

        if (lastCompleteIndex > -1) {
          cleanedString = cleanedString.substring(0, lastCompleteIndex + 1)
        }
      }

      var obj = JSON.parse(cleanedString)
      return obj
    } catch (error) {
      console.error(
        'Unable to parse json string to object :: ' + jsonString,
        error,
      )
      return ''
    }
  },

  validateMedicalRecord: (obj) => {
    const summaryInfo = process.env.SummaryInfo
    const newKeysArray = summaryInfo.split(',').map((key) => key.trim())
    // Add the additional required fields
    const additionalFields = [
      'vitals',
      'anthropometry',
      'generalphysicalexamination',
      'heent',
      'systemicexamination',
    ]
    const allRequiredKeys = [...newKeysArray, ...additionalFields]
    // const requiredKeys  = ["presentingcomplaint", "pasthistory", "familyhistory", "socialhistory", "situationalhistory", "habitualhistory", "diagnosis", "recommendations"];
    return allRequiredKeys.every((key) => key in obj)
  },

  jsonResponse: (bodyContent, statusCode = 200) => {
    const response = new HttpResponse({
      jsonBody: bodyContent,
      status: statusCode,
    })
    response.headers.set('content-type', 'application/json')
    return response
  },
  encryptData: (data) => {
    const cipher = crypto.createCipheriv('aes-256-cbc', secretKey, iv)
    let encrypted = cipher.update(data, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    return encrypted
  },
  decryptData: (data) => {
    const decipher = crypto.createDecipheriv('aes-256-cbc', secretKey, iv)
    let decrypted = decipher.update(data, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    return decrypted
  },
  encryptDataWithRandomIV: (data) => {
    const randomIV = crypto.randomBytes(16) // Generate random 16-byte IV
    const cipher = crypto.createCipheriv('aes-256-cbc', secretKey, randomIV)
    let encrypted = cipher.update(data, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    // Prepend IV to encrypted data for storage/transmission
    return randomIV.toString('hex') + ':' + encrypted
  },
  decryptDataWithRandomIV: (data) => {
    const parts = data.split(':')
    if (parts.length !== 2) {
      throw new Error('Invalid encrypted data format')
    }
    const iv = Buffer.from(parts[0], 'hex')
    const encryptedData = parts[1]
    const decipher = crypto.createDecipheriv('aes-256-cbc', secretKey, iv)
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    return decrypted
  },
}

module.exports = helper

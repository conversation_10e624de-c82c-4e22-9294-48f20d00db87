const paymentService = require('../services/payment-service')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const logging = require('../common/logging')
const { PaymentType } = require('../common/constant')

class PaymentHandler {
  async createOrder(req) {
    try {
      const paymentData = await req.json()

      const requiredFields = [
        'amount',
        'paymentType',
        'patientId',
        'organizationId',
        'description',
      ]
      const missingFields = requiredFields.filter(
        (field) => !paymentData[field],
      )

      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest,
        )
      }

      if (!Object.values(PaymentType).includes(paymentData.paymentType)) {
        return jsonResponse(
          `Invalid payment type. Allowed values: ${Object.values(
            PaymentType,
          ).join(', ')}`,
          HttpStatusCode.BadRequest,
        )
      }

      paymentData.amount = Math.round(paymentData.amount * 100)
      const result = await paymentService.createOrder(paymentData)

      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in createOrder handler', error)
      return jsonResponse(
        'Failed to create payment order',
        HttpStatusCode.InternalServerError,
      )
    }
  }
  async verifyPayment(req) {
    try {
      const verificationData = await req.json()

      const requiredFields = [
        'razorpay_order_id',
        'razorpay_payment_id',
        'razorpay_signature',
      ]
      const missingFields = requiredFields.filter(
        (field) => !verificationData[field],
      )

      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest,
        )
      }

      const result = await paymentService.verifyPayment(verificationData)

      if (result.verified) {
        return jsonResponse(result, HttpStatusCode.Ok)
      } else {
        return jsonResponse(result, HttpStatusCode.BadRequest)
      }
    } catch (error) {
      logging.logError('Error in verifyPayment handler', error)
      return jsonResponse(
        'Payment verification failed',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async handleWebhook(req) {
    try {
      const signature = req.headers.get('x-razorpay-signature')

      if (!signature) {
        return jsonResponse(
          'Missing webhook signature',
          HttpStatusCode.BadRequest,
        )
      }

      const webhookData = await req.json()

      const result = await paymentService.handleWebhook(webhookData, signature)

      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in handleWebhook handler', error)
      return jsonResponse(
        'Webhook processing failed',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getPaymentDetails(req) {
    try {
      const id = req.query.get('id')

      if (!id) {
        return jsonResponse(
          'Missing paymentId parameter',
          HttpStatusCode.BadRequest,
        )
      }

      const payment = await paymentService.getPaymentById(id)

      return jsonResponse(payment, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getPaymentDetails handler', error)

      if (error.message.includes('not found')) {
        return jsonResponse('Payment not found', HttpStatusCode.NotFound)
      }

      return jsonResponse(
        'Failed to fetch payment details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getOrganizationPayments(req) {
    try {
      const organizationId = req.query.get('organizationId')
      const pageSize = parseInt(req.query.get('pageSize')) || 20
      const continuationToken = req.query.get('continuationToken')

      const filters = {
        patientId: req.query.get('patientId'),
        status: req.query.get('status'),
        paymentType: req.query.get('paymentType'),
      }

      const result = await paymentService.getPaymentsByOrganization(
        organizationId,
        pageSize,
        continuationToken,
        filters,
      )

      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getOrganizationPayments handler', error)
      return jsonResponse(
        'Failed to fetch organization payments',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getPaymentStatistics(req) {
    try {
      const organizationId = req.query.get('organizationId')

      if (!organizationId) {
        return jsonResponse(
          'Missing organizationId parameter',
          HttpStatusCode.BadRequest,
        )
      }

      const stats = await paymentService.getPaymentStatistics(organizationId)

      return jsonResponse(stats, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getPaymentStatistics handler', error)
      return jsonResponse(
        'Failed to fetch payment statistics',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async searchPayments(req) {
    try {
      const filters = {
        organizationId: req.query.get('organizationId'),
        patientId: req.query.get('patientId'),
        status: req.query.get('status'),
        paymentType: req.query.get('paymentType'),
        startDate: req.query.get('startDate'),
        endDate: req.query.get('endDate'),
        minAmount: req.query.get('minAmount')
          ? parseInt(req.query.get('minAmount')) * 100
          : null, 
        maxAmount: req.query.get('maxAmount')
          ? parseInt(req.query.get('maxAmount')) * 100
          : null,
      }

      Object.keys(filters).forEach((key) => {
        if (
          filters[key] === null ||
          filters[key] === undefined ||
          filters[key] === ''
        ) {
          delete filters[key]
        }
      })

      if (!filters.organizationId) {
        return jsonResponse(
          'Missing organizationId parameter',
          HttpStatusCode.BadRequest,
        )
      }

      const payments = await paymentService.getPaymentsByOrganization(
        filters.organizationId,
        20,
        null,
        filters
      )

      return jsonResponse(payments, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in searchPayments handler', error)
      return jsonResponse(
        'Failed to search payments',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new PaymentHandler()

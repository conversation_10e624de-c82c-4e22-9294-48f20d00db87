const labTestService = require('../services/patient-lab-test-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')

class LabTestHandler {
  async getLabTests(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || null
      const sortField = req.query.get('sortField') || null
      const sortOrder = req.query.get('sortOrder') || 'asc'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null
      const searchText = req.query.get('searchText') || null
      const department = req.query.get('department') || 'ALL'

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const data = await labTestService.getLabTestsByPatient(
        patientId,
        dateFilter,
        sortField,
        sortOrder,
        customDateRange,
        searchText,
        department,
      )

      return jsonResponse(data)
    } catch (err) {
      console.error('Error fetching lab tests:', err)
      return jsonResponse(
        'Failed to fetch lab tests',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async createLabTest(req) {
    try {
      const body = await req.json()
      const { patientId, labTests } = body
      if (!patientId || !Array.isArray(labTests) || labTests.length === 0) {
        return jsonResponse(
          'Invalid data for lab tests',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await labTestService.createLabTestsForPatient(
        patientId,
        labTests,
      )
      return jsonResponse(result, HttpStatusCode.Created)
    } catch (err) {
      console.error('Error creating lab tests:', err)
      return jsonResponse(
        'Failed to create lab tests',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updateLabTest(req) {
    try {
      const data = await req.json()
      const result = await labTestService.updateLabTest(data)
      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        'Failed to update lab test',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async deleteLabTest(req) {
    try {
      const id = req.query.get('id')
      const result = await labTestService.deleteLabTest(id)
      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        'Failed to delete lab test',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getLabTestDetails(req) {
    try {
      const labTestId = req.query.get('labTestId')
      if (!labTestId) {
        return jsonResponse(
          'Missing patient ID or labtest ID',
          HttpStatusCode.BadRequest,
        )
      }

      const labTest = await labTestService.getLabTestById(labTestId)
      return jsonResponse(labTest)
    } catch (err) {
      return jsonResponse(
        'Error fetching labtest details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async searchPatientLabTest(req) {
    try {
      const body = await req.json()
      const {
        searchText = '',
        pageSize = 10,
        continuationToken = '',
        patientId = null,
      } = body

      if (!searchText.trim()) {
        return jsonResponse('Missing search text', HttpStatusCode.BadRequest)
      }

      const result = await labTestService.searchPatientLabTest(
        searchText,
        pageSize,
        continuationToken,
        patientId,
      )

      return jsonResponse(result)
    } catch (err) {
      console.error('Search lab test error:', err)
      return jsonResponse(
        'Error searching lab test',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // /**
  //  * Create lab test order with payment integration
  //  * Calculates total test cost and creates payment order if required
  //  */
  // async createLabTestWithPayment(req) {
  //   try {
  //     const body = await req.json()
  //     const { patientId, labTests, requirePayment = false } = body

  //     if (!patientId || !Array.isArray(labTests) || labTests.length === 0) {
  //       return jsonResponse(
  //         'Invalid data for lab tests',
  //         HttpStatusCode.BadRequest,
  //       )
  //     }

  //     // Get patient details
  //     const patient = await patientService.GetPatientById(patientId)
  //     if (!patient) {
  //       return jsonResponse('Patient not found', HttpStatusCode.NotFound)
  //     }

  //     // Calculate total lab test cost
  //     const totalLabTestCost = this.calculateLabTestCost(labTests)

  //     // If payment is required and total cost > 0, create payment order first
  //     if (requirePayment && totalLabTestCost > 0) {
  //       // Generate lab test order ID
  //       const labTestOrderId = uuidv4()

  //       // Create payment order for lab test cost
  //       const paymentData = {
  //         amount: totalLabTestCost, // Amount in rupees
  //         currency: 'INR',
  //         paymentType: PaymentType.LAB_TEST,
  //         patientId: patient.id,
  //         organizationId: patient.organizationId,
  //         description: `Lab Tests for ${patient.name}`,
  //         metadata: {
  //           labTestOrderId: labTestOrderId,
  //           patientName: patient.name,
  //           testIds: labTests.map((test) => test.testId || test.id),
  //           testNames: labTests.map((test) => test.testName || test.name),
  //           testCount: labTests.length,
  //           totalCost: totalLabTestCost,
  //         },
  //       }

  //       const paymentOrder = await paymentService.createOrder(paymentData)

  //       // Return payment order details for frontend to process
  //       return jsonResponse({
  //         requiresPayment: true,
  //         totalLabTestCost: totalLabTestCost,
  //         paymentOrder: paymentOrder.data,
  //         labTestOrderId: labTestOrderId,
  //         testCount: labTests.length,
  //         message: 'Payment required for lab tests',
  //       })
  //     } else {
  //       // No payment required, create lab tests directly
  //       return await this.createLabTest(req)
  //     }
  //   } catch (err) {
  //     console.error('Lab test with payment error:', err)
  //     return jsonResponse(
  //       'Error creating lab test with payment',
  //       HttpStatusCode.InternalServerError,
  //     )
  //   }
  // }

  // /**
  //  * Complete lab test order after successful payment
  //  * Called after payment verification is successful
  //  */
  // async completeLabTestOrder(req) {
  //   try {
  //     const body = await req.json()
  //     const { patientId, labTests, paymentId, labTestOrderId } = body

  //     if (!paymentId) {
  //       return jsonResponse('Payment ID is required', HttpStatusCode.BadRequest)
  //     }

  //     // Verify payment is completed
  //     const payment = await paymentService.getPaymentById(paymentId)
  //     if (!payment || !payment.isSuccessful()) {
  //       return jsonResponse(
  //         'Payment not completed or not found',
  //         HttpStatusCode.BadRequest,
  //       )
  //     }

  //     // Ensure lab test data matches payment metadata
  //     if (
  //       payment.metadata.labTestOrderId &&
  //       labTestOrderId !== payment.metadata.labTestOrderId
  //     ) {
  //       return jsonResponse(
  //         'Lab test order ID mismatch with payment record',
  //         HttpStatusCode.BadRequest,
  //       )
  //     }

  //     // Create lab tests with confirmed payment
  //     const result = await labTestService.createLabTestsForPatient(
  //       patientId,
  //       labTests,
  //     )

  //     return jsonResponse(
  //       {
  //         success: true,
  //         labTests: result,
  //         paymentId: paymentId,
  //         message: 'Lab tests ordered successfully with payment',
  //       },
  //       HttpStatusCode.Created,
  //     )
  //   } catch (err) {
  //     console.error('Complete lab test order error:', err)
  //     return jsonResponse(
  //       'Error completing lab test order',
  //       HttpStatusCode.InternalServerError,
  //     )
  //   }
  // }

  // /**
  //  * Calculate total cost of lab tests
  //  * Helper method to compute payment amount
  //  */
  // calculateLabTestCost(labTests) {
  //   let totalCost = 0

  //   labTests.forEach((test) => {
  //     if (test.cost && !isNaN(parseFloat(test.cost))) {
  //       const quantity = parseInt(test.qty) || 1
  //       const unitCost = parseFloat(test.cost)
  //       totalCost += unitCost * quantity
  //     }
  //   })

  //   return Math.round(totalCost * 100) / 100 // Round to 2 decimal places
  // }

  // /**
  //  * Get lab test cost estimate
  //  * Helper method for frontend to display cost before payment
  //  */
  // async getLabTestCostEstimate(req) {
  //   try {
  //     const body = await req.json()
  //     const { labTests } = body

  //     if (!Array.isArray(labTests) || labTests.length === 0) {
  //       return jsonResponse('Invalid lab tests data', HttpStatusCode.BadRequest)
  //     }

  //     const totalCost = this.calculateLabTestCost(labTests)
  //     const testBreakdown = labTests.map((test) => ({
  //       name: test.testName || test.name,
  //       quantity: parseInt(test.qty) || 1,
  //       unitCost: parseFloat(test.cost) || 0,
  //       totalCost: (parseFloat(test.cost) || 0) * (parseInt(test.qty) || 1),
  //     }))

  //     return jsonResponse({
  //       totalCost: totalCost,
  //       testCount: labTests.length,
  //       testBreakdown: testBreakdown,
  //       requiresPayment: totalCost > 0,
  //     })
  //   } catch (err) {
  //     console.error('Get lab test cost estimate error:', err)
  //     return jsonResponse(
  //       'Error calculating lab test cost',
  //       HttpStatusCode.InternalServerError,
  //     )
  //   }
  // }
}

module.exports = new LabTestHandler()

const appointmentService = require('../services/appointment-service')
const patientService = require('../services/patient-service')
const userService = require('../services/user-service')
const {
  PatientQueueLabStatus,
  PatientQueueStatus,
  AppointmentStatus,
} = require('../common/constant')
const patientLabTestService = require('../services/patient-lab-test-service')
const paymentService = require('../services/payment-service')
const { logError } = require('../common/logging')

class AppointmentManageHandler {
  async deleteQueue(queueId) {
    return await appointmentService.deleteQueue(queueId)
  }

  async getPatientDoctorVisitType(patientId, doctorId) {
    const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.status != 'canceled'`
    const queues = await appointmentService.getQueueByQuery(query)

    const appointmentIds = queues.map((queue) => queue.appointmentId)
    const appointments = await appointmentService.getAppointmentByQuery(
      `SELECT * FROM c WHERE c.id IN (${appointmentIds
        .map((id) => `'${id}'`)
        .join(', ')}) AND c.doctorId = '${doctorId}'`,
    )

    if (appointments && appointments.length > 0) {
      return 'returning'
    }
    return 'new'
  }

  async addAppointment(appointment, created_by) {
    // Get user to get organization
    const user = await userService.getUserById(created_by)
    if (!user || !user.organizationId) {
      throw new Error('User organization not found. Cannot create appointment.')
    }
    const organizationId = user.organizationId

    // Format the current date once to reuse for ID generation

    if (appointment.date === 'Invalid Date') {
      throw new Error('Invalid date')
    }
    const now = new Date()
    const dateString = `${now.getFullYear()}${String(
      now.getMonth() + 1,
    ).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(
      now.getMilliseconds(),
    ).padStart(3, '0')}`

    const dateOnly = appointment.date.split('T')[0]

    const query = `
        SELECT * FROM c
        WHERE c.doctorId = '${appointment.doctorId}'
            AND STARTSWITH(c.date, '${dateOnly}')
        `

    const existingAppointments = await appointmentService.getAppointmentByQuery(
      query,
    )

    if (!existingAppointments || existingAppointments.length === 0) {
      const appointmentId = appointment?.id || `APM${dateString}`
      const queueId = `QUEUE${dateString}`

      const apm = {
        id: appointmentId,
        doctorId: appointment.doctorId,
        date: appointment.date,
        created_by,
        updated_by: created_by,
        type: appointment?.type,
        patientId: appointment.patientId,
        time: appointment?.time,
        department: appointment?.department,
        organizationId: organizationId, // Add organizationId
      }

      const queue = {
        id: queueId,
        appointmentId: appointmentId,
        patientId: appointment.patientId,
        time: appointment.time,
        status: appointment.status,
        queuePosition: 1, // Assuming the position is 1 for new queues
        created_by,
        updated_by: created_by,
        type: appointment?.type,
        date: appointment.date,
        doctorId: appointment.doctorId,
        department: appointment?.department,
        paymentStatus: appointment?.paymentStatus || null,
        consultationFee: appointment?.consultationFee || null,
        organizationId: organizationId, // Add organizationId
      }

      const resApm = await appointmentService.createAppointment(apm)
      const resQueue = await appointmentService.createQueue(queue)
      const data = await this.getAppointmentDetails(apm.id)
      return data
    } else {
      var queues = await appointmentService.getQueueByQuery(
        `SELECT * FROM c WHERE c.appointmentId = '${existingAppointments[0].id}'`,
      )

      // Update existing appointment with organizationId if it doesn't have one
      const existingAppointment = existingAppointments[0]
      if (!existingAppointment.organizationId) {
        existingAppointment.organizationId = organizationId
        existingAppointment.updated_by = created_by
        existingAppointment.updated_on = new Date().toISOString()
        await appointmentService.updateAppointment(existingAppointment)
        console.log('Updated existing appointment with organizationId:', organizationId)
      }

      const queueId = `QUEUE${dateString}`
      const queue = {
        id: queueId,
        appointmentId: existingAppointments[0].id,
        patientId: appointment.patientId,
        time: appointment.time,
        status: appointment.status,
        queuePosition: queues.length + 1,
        created_by,
        updated_by: created_by,
        type: appointment?.type,
        doctorId: appointment.doctorId,
        date: appointment.date,
        department: appointment?.department,
        paymentStatus: appointment?.paymentStatus || null,
        consultationFee: appointment?.consultationFee || null,
        organizationId: organizationId, // Add organizationId
      }

      const resQueue = await appointmentService.createQueue(queue)
      console.log('resQueue', resQueue)
      const data = await this.getAppointmentDetails(queue.appointmentId)
      return data
    }
  }

  async getAppointment(doctorId, date) {
    var query = `SELECT * FROM c WHERE c.doctorId = '${doctorId}' AND STARTSWITH(c.date, '${date}')`
    var data = await appointmentService.getAppointmentByQuery(query)
    if (data.length > 0) {
      return data[0]
    }
    return data
  }

  async getAppointments(doctorId) {
    var query = `SELECT * FROM c WHERE c.doctorId = '${doctorId}'`
    var data = await appointmentService.getAppointmentByQuery(query)
    var lst = []
    for (let i = 0; i < data.length; i++) {
      const e = data[i]
      if (data && data.length > 0) {
        var res = await this.getAppointmentDetails(e.id)
        lst.push(res)
      }
    }
    return lst
  }

  async updateAppointment(appointment, updated_by) {
    appointment.updated_by = updated_by
    var res = await appointmentService.updateAppointment(appointment)
    return res
  }

  async createQueue(queue, created_by) {
    // Get user to get organization
    const user = await userService.getUserById(created_by)
    if (!user || !user.organizationId) {
      throw new Error('User organization not found. Cannot create queue.')
    }
    
    queue.created_by = created_by
    queue.updated_by = created_by
    queue.organizationId = user.organizationId 
    var res = await appointmentService.createQueue(queue)
    return res
  }

  async getAppointmentDetails(appointmentId) {
    var appointments = await appointmentService.getAppointmentByQuery(
      `SELECT * FROM c WHERE c.id = '${appointmentId}'`,
    )
    var appointment = appointments[0]
    var queues = await appointmentService.getQueueByQuery(
      `SELECT * FROM c WHERE c.appointmentId = '${appointmentId}' and c.status != 'canceled'`,
    )
    const patientIds = queues?.map((queue) => queue.patientId)
    if (patientIds.length === 0) {
      return {
        appointmentId: appointment.id,
        doctorId: appointment.doctorId,
        date: appointment.date,
        department: appointment.department || '',
        type: appointment?.type,
        queues: [],
      }
    }
    var patients = await patientService.QueryPatientProfile(
      `SELECT * FROM c WHERE c.id IN (${patientIds
        .map((id) => `'${id}'`)
        .join(', ')})`,
    )

    // Combine results
    const queuesWithDetails = await Promise.all(
      queues.map(async (queue) => {
        const patient = patients.find((p) => p.id === queue.patientId)
        const labStatus =
          await patientLabTestService.getPatientLatestLabTestStatus(
            queue.patientId,
          )
        const paymentStatus = await this.getPaymentStatusForAppointment(
          patient.id,
          queue.appointmentId,
          queue.id,
        )

        return {
          queueId: queue.id,
          patientId: patient.id,
          patientName: patient.name,
          patientAge: (() => {
            const y =
              new Date().getFullYear() - new Date(patient.dob).getFullYear()
            return Number.isFinite(y) ? y : patient.age ?? null
          })(),
          patientAddress: patient.address,
          patientPhone: patient.contact.phone,
          patientEmail: patient.contact.email,
          patientSex: patient.sex,
          patientDoB: patient.dob,
          time: queue.time,
          status: queue.status,
          department: queue.department || '',
          queuePosition: queue.queuePosition,
          type: queue?.type,
          date: appointment.date,
          labStatus: labStatus ?? PatientQueueLabStatus.NO_TEST_ORDERED,
          paymentStatus: paymentStatus || queue.paymentStatus || null,
          consultationFee: queue.consultationFee || null,
        }
      }),
    )

    const result = {
      appointmentId: appointment.id,
      doctorId: appointment.doctorId,
      date: appointment.date,
      department: appointment.department || '',
      type: appointment?.type,
      queues: queuesWithDetails,
    }
    return result
  }

  async upsertAppointment(appointmentId, appointmentData) {
    try {
      var data = await appointmentService.upsertAppointment(
        appointmentId,
        appointmentData,
      )
      return data
    } catch (error) {
      logError(``, error)
      return null
    }
  }

  async updateQueue(queueId, updateData, updated_by) {
    updateData.updated_by = updated_by

    if (updateData?.status === 'Booked-Arrived') {
      updateData.patientArrivalTime = new Date().toISOString()
    }

    if (updateData?.status === 'Consultation-Arrived') {
      updateData.consultationStartTime = new Date().toISOString()
    }

    if (updateData?.status === 'Consultation-Done') {
      updateData.consultationEndTime = new Date().toISOString()
    }

    var res = await appointmentService.upsertQueue(queueId, updateData)
    var appointment = await this.getAppointmentDetails(res.appointmentId)
    return appointment
  }

  async updateQueueById(queueId, updateData, updated_by) {
    updateData.updated_by = updated_by

    if (updateData?.date) {
      const dateOnly = updateData?.date?.split('T')[0]
      const queue = await appointmentService.getQueueById(queueId)
      const queueData = { ...queue?.[0], ...updateData }
      const query = `
                SELECT * FROM c
                WHERE c.doctorId = '${queueData.doctorId}'
                    AND STARTSWITH(c.date, '${dateOnly}')
                `
      const existingAppointments =
        await appointmentService.getAppointmentByQuery(query)
      if (existingAppointments.length > 0) {
        updateData.appointmentId = existingAppointments[0].id
      } else {
        const now = new Date()
        const dateString = `${now.getFullYear()}${String(
          now.getMonth() + 1,
        ).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(
          now.getMilliseconds(),
        ).padStart(3, '0')}`
        const appointmentId = `APM${dateString}`
        const apm = {
          id: appointmentId,
          doctorId: queueData.doctorId,
          date: queueData.date,
          created_by: updated_by,
          updated_by: updated_by,
          type: queueData?.type,
          patientId: queueData.patientId,
          time: queueData?.time,
          department: queueData?.department,
        }
        const resApm = await appointmentService.createAppointment(apm)
        updateData.appointmentId = resApm.id
      }
    }
    var res = await appointmentService.upsertQueue(queueId, updateData)
    var appointment = await this.getAppointmentDetails(res.appointmentId)
    return appointment
  }

  async upsertQueueOnly(queueId, updateData, updated_by) {
    updateData.updated_by = updated_by
    var res = await appointmentService.upsertQueue(queueId, updateData)
    return res
  }

  async getQueueById(queueId) {
    var data = await appointmentService.getQueueById(queueId)
    if (data && data.length > 0) {
      return data[0]
    }
    return data
  }

  async getPaymentStatusForAppointment(patientId, appointmentId, queueId) {
    try {
      const queue = await appointmentService.getQueueById(queueId)
      if (queue && queue.length > 0 && queue[0].paymentStatus) {
        return queue[0].paymentStatus
      }

      const payments = await paymentService.getPaymentsByOrganization(
        null,
        100,
        null,
        {
          patientId: patientId,
        },
      )

      const consultationPayments = payments.payments.filter(
        (payment) =>
          payment.paymentType === 'consultation' &&
          payment.status === 'completed' &&
          payment.metadata &&
          (payment.metadata.appointmentId === appointmentId ||
            payment.metadata.queueId === queueId),
      )

      if (consultationPayments.length > 0) {
        return 'PAID'
      }

      return null
    } catch (error) {
      console.error('Error getting payment status:', error)
      return null
    }
  }

  async getFutureAppointmentsByPatientId(patientId, now) {
    const nowDate = typeof now === 'string' ? new Date(now) : now
    const queues = await appointmentService.getQueueByQuery(
      `SELECT * FROM c
            WHERE c.patientId = '${patientId}'
            AND NOT CONTAINS(c.status, '-${PatientQueueStatus.NO_SHOW}')
            AND NOT CONTAINS(c.status, '${AppointmentStatus.DONE}-')`,
    )

    const filteredQueue = queues.filter(
      (queue) => new Date(queue.date) > nowDate,
    )

    // Add payment status to each queue item
    const formattedQueue = await Promise.all(
      filteredQueue.map(async ({ id, ...queue }) => {
        const paymentStatus = await this.getPaymentStatusForAppointment(
          patientId,
          queue.appointmentId,
          id,
        )
        return {
          queueId: id,
          ...queue,
          paymentStatus: paymentStatus || queue.paymentStatus || null,
          consultationFee: queue.consultationFee || null,
        }
      }),
    )

    return formattedQueue
  }
}

module.exports = new AppointmentManageHandler()

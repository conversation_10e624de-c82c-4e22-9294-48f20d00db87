const {
  getTestsByPackageId,
  getTestPackageById,
  getTestPackageByName,
  getTestPackagesByType,
  getUserSpecificPackages,
} = require('../queries/test-package-query')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const testPackageContainer = 'test-packages'

class TestPackageRepository {
  async getTestsByPackageId(packageId) {
    const query = getTestsByPackageId(packageId)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }

  async getTestPackageById(packageId) {
    const query = getTestPackageById(packageId)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }

  async getTestPackageByName(name) {
    const query = getTestPackageByName(name)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }

  async createTestPackage(testPackageData) {
    return cosmosDbContext.createItem(testPackageData, testPackageContainer)
  }

  async upsertTestPackage(testPackageData) {
    return cosmosDbContext.upsertItem(
      testPackageData.id,
      testPackageData,
      testPackageContainer,
    )
  }

  async getTestPackagesByType(type) {
    const query = getTestPackagesByType(type)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }

  async getUserSpecificPackages(userId) {
    const query = getUserSpecificPackages(userId)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }
}

module.exports = new TestPackageRepository()

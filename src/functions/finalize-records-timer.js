const { app } = require('@azure/functions');
const patientService = require('../services/patient-service');

app.timer('finalize-patient-lifestyle-records', {
    schedule: '0 0 * * * *', // Every hour at minute 0
    handler: async (myTimer, context) => {
        context.log('[Timer] Patient lifestyle finalization job running...');

        try {
            const cutoffDate = new Date(
                Date.now() - 24 * 60 * 60 * 1000,
            ).toISOString();
            const query = `SELECT * FROM c WHERE (NOT IS_DEFINED(c.status) OR c.status != 'finalized') AND c.created_on < '${cutoffDate}'`;

            const records = await patientService.getPatientLifeStyleByQuery(query);
            if (!records?.length) {
                context.log('No records to finalize');
                return;
            }

            for (const record of records) {
                await patientService.patchPatientLifeStyle(record.id, {
                    status: 'finalized',
                    updated_on: new Date().toISOString(),
                });
                context.log(`Finalized record ${record.id}`);
            }
        } catch (error) {
            context.log('Timer job failed:', error);
        }
    }
});
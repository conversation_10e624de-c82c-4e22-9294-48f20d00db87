const { app } = require('@azure/functions');
const fetch = require('node-fetch');
const { jsonResponse } = require('../common/helper');
const openAIService = require('../services/openai-service');
const { HttpStatusCode } = require('axios');

app.http('lifestyle-summary', {
  methods: ['POST'],
  authLevel: 'anonymous',
  route: 'lifestyle/summary',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`);
    try {
      const body = await req.json();
      const { transcript, source } = body;
      if (!transcript || !source) {
        return jsonResponse('Missing transcript or source', HttpStatusCode.BadRequest);
      }

      // Fetch questions from the existing lifestyle-question API
      // Assuming the API is available locally, otherwise update the URL
      const apiUrl = `${process.env.INTERNAL_API_BASE || 'http://localhost:7071/api'}/lifestyle/question?source=${encodeURIComponent(source)}`;
      const questionsRes = await fetch(apiUrl, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });
      if (!questionsRes.ok) {
        return jsonResponse('Failed to fetch questions from DB', HttpStatusCode.InternalServerError);
      }
      const questionsData = await questionsRes.json();

      // For each field, extract the answer from the transcript
      if (Array.isArray(questionsData.questions)) {
        for (const questionGroup of questionsData.questions) {
          if (!Array.isArray(questionGroup.questions)) continue;
          for (const question of questionGroup.questions) {
            if (!Array.isArray(question.fields)) continue;
            for (const field of question.fields) {
              // Compose prompt for OpenAI
              const prompt = `From this transcript: "${transcript}", answer the question: "${field.label}". If not answered, reply "Not mentioned".`;
              try {
                const aiAnswer = await openAIService.chatCompletion(prompt);
                field.transcription = aiAnswer;
              } catch (err) {
                field.transcription = 'Error extracting answer';
                context.log('Error with OpenAI for field', field.label, err);
              }
            }
          }
        }
      }

      return jsonResponse({ questions: questionsData.questions }, HttpStatusCode.Ok);
    } catch (err) {
      context.log('Error in lifestyle-summary handler', err);
      return jsonResponse('Internal server error', HttpStatusCode.InternalServerError);
    }
  }
});

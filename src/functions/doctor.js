const { app } = require('@azure/functions')
const { HttpMethod } = require('../common/constant')
const doctorHandler = require('../handlers/doctor-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const {
  findBlobByPrefix,
  deleteBlobsByPrefix,
  uploadBlob,
  generateBlobSASUrl,
} = require('../utils/blobUtils')
const { HTTPMethod } = require('@azure/cosmos')

const containerName = 'profile-pictures'

app.http('doctor', {
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed req for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')

    switch (req.method) {
      case HttpMethod.get:
        const id = req.query.get('id')
        let email = req.query.get('email')
        var data = null
        if (email) {
          // Handle URL decoding issue where + in email gets decoded as space in query parameters
          // Use the same pattern as user API for consistency
          if (email.includes(' ')) {
            const emailPattern = /^[^\s@]+(\s[^\s@]*)*@[^\s@]+\.[^\s@]+$/
            if (emailPattern.test(email)) {
              email = email.replace(/ /g, '+')
            }
          }
          data = await doctorHandler.getDoctorByEmail(email)
          return jsonResponse(data)
        }
        if (!id) {
          return jsonResponse(`Missing doctorId`, HttpStatusCode.BadRequest)
        }
        data = await doctorHandler.getDoctorById(id)
        return jsonResponse(data)
      case HttpMethod.post:
      case HttpMethod.put:
        var data = null
        if (!req.body) {
          return jsonResponse(
            `Missing request payload`,
            HttpStatusCode.BadRequest,
          )
        }
        const doctor = await req.json()
        if (req.method == HttpMethod.post) {
          data = await doctorHandler.createDoctor(doctor, decode.oid)
        } else {
          data = await doctorHandler.updateDoctor(doctor, decode.oid)
        }
        if (!data) {
          return jsonResponse(
            `Unable to create doctor`,
            HttpStatusCode.InternalServerError,
          )
        }
        return jsonResponse(data)
      case HttpMethod.delete:
        const deleteId = req.query.get('id')
        if (!deleteId) {
          return jsonResponse(`Missing doctorId`, HttpStatusCode.BadRequest)
        }
        const deleteResult = await doctorHandler.deleteDoctor(deleteId)
        if (!deleteResult) {
          return jsonResponse(
            `Unable to delete doctor`,
            HttpStatusCode.InternalServerError,
          )
        }
        return jsonResponse({
          success: true,
          message: 'Doctor deleted successfully',
        })
      case HttpMethod.patch:
        var data = null
        var doctorId = null
        if (!req.body) {
          return jsonResponse(
            `Missing request payload`,
            HttpStatusCode.BadRequest,
          )
        }
        if (!req.query.get('id')) {
          return jsonResponse(`Missing ID`, HttpStatusCode.BadRequest)
        }
        doctorId = req.query.get('id')
        var doctorUpdate = await req.json()
        data = await doctorHandler.upsertDoctor(
          doctorId,
          doctorUpdate,
          decode.oid,
        )
        if (!data) {
          return jsonResponse(
            `Unable to update doctor`,
            HttpStatusCode.InternalServerError,
          )
        }
        return jsonResponse(data)

      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})

app.http('doctor-profile-picture-upload', {
  methods: ['POST'],
  authLevel: 'function',
  route: 'doctor/profile-picture/upload',
  handler: async (request, context) => {
    context.log(`Http function processed request for url "${request.url}"`)
    const data = await request.formData()
    const file = data.get('file')
    const doctorId = data.get('doctorId')

    if (!file || !doctorId) {
      return jsonResponse(`Missing file or doctorId`, HttpStatusCode.BadRequest)
    }

    if (file.size > 5242880) {
      return jsonResponse(
        `File size should be less than 5MB`,
        HttpStatusCode.BadRequest,
      )
    }

    const prefix = `doctor/${doctorId}/`
    await deleteBlobsByPrefix(containerName, prefix)

    const blobName = `${prefix}${file.name}`
    const blobUrl = await uploadBlob(
      containerName,
      blobName,
      await file.arrayBuffer(),
    )
    return jsonResponse({ profilePictureUrl: blobUrl })
  },
})

app.http('get-doctor-profile-picture-url', {
  methods: ['GET', 'DELETE'],
  authLevel: 'function',
  route: 'doctor/profile-picture/url',
  handler: async (request, context) => {
    try {
      context.log(
        `Processing ${request.method} request for doctor profile picture URL`,
      )

      const doctorId = request.query.get('doctorId')

      if (!doctorId) {
        context.log.error('Missing doctorId parameter')
        return jsonResponse(`Missing doctorId`, HttpStatusCode.BadRequest)
      }

      // Validate environment variables
      if (!process.env.AZURE_STORAGE_ACCOUNT_NAME) {
        context.log.error(
          'AZURE_STORAGE_ACCOUNT_NAME environment variable is not set',
        )
        return jsonResponse(
          `Storage configuration error`,
          HttpStatusCode.InternalServerError,
        )
      }

      const prefix = `doctor/${doctorId}/`
      context.log(`Using blob prefix: ${prefix}`)

      if (request.method === HttpMethod.get) {
        try {
          const blobName = await findBlobByPrefix(containerName, prefix)
          if (!blobName) {
            context.log(`Profile picture not found for doctor: ${doctorId}`)
            return jsonResponse(
              `Profile picture not found`,
              HttpStatusCode.NotFound,
            )
          }

          const blobUrl = generateBlobSASUrl(containerName, blobName)
          context.log(
            `Successfully retrieved profile picture URL for doctor: ${doctorId}`,
          )
          return jsonResponse({ profilePictureUrl: blobUrl })
        } catch (error) {
          context.log.error(
            `Error finding profile picture for doctor ${doctorId}: ${error.message}`,
            error,
          )
          return jsonResponse(
            `Error retrieving profile picture`,
            HttpStatusCode.InternalServerError,
          )
        }
      } else if (request.method === HttpMethod.delete) {
        try {
          await deleteBlobsByPrefix(containerName, prefix)
          context.log(
            `Successfully deleted profile picture for doctor: ${doctorId}`,
          )
          return jsonResponse({
            success: true,
            message: 'Profile picture deleted successfully',
          })
        } catch (error) {
          context.log.error(
            `Error deleting profile picture for doctor ${doctorId}: ${error.message}`,
            error,
          )
          return jsonResponse(
            `Error deleting profile picture`,
            HttpStatusCode.InternalServerError,
          )
        }
      } else {
        context.log.error(`Unsupported HTTP method: ${request.method}`)
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
      }
    } catch (error) {
      context.log.error(
        `Unexpected error in get-doctor-profile-picture-url: ${error.message}`,
        error,
      )
      return jsonResponse(
        `Internal server error`,
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

const { app } = require('@azure/functions')
const {
  BlobServiceClient,
  generateBlobSASQueryParameters,
  BlobSASPermissions,
  StorageSharedKeyCredential,
} = require('@azure/storage-blob')
const { encryptBuffer } = require('../services/encryptionService')
const {
  saveLabReportMetadata,
  updateLabReportMetadata,
  deleteLabReportMetadata,
} = require('../services/cosmosService')
const { v4: uuidv4 } = require('uuid')
const axios = require('axios')
const { getLabReportMetadata } = require('../services/cosmosService')
const { decryptBuffer } = require('../services/encryptionService')
const { fileTypeFromBuffer } = require('file-type')
const labTestService = require('../services/patient-lab-test-service')
const { LabTestStatus } = require('../common/constant')
const FormData = require('form-data')

const OCR_SERVICE_URL =
  'http://ade-fastapi-app-unique.eastus.azurecontainer.io:8000/upload/'

// Function to send document to OCR service
async function processDocumentWithOCR(buffer, fileName, context) {
  try {
    context.log('Starting OCR processing for file:', fileName)

    const formData = new FormData()
    formData.append('file', buffer, {
      filename: fileName,
      contentType: 'application/pdf',
    })

    const response = await axios.post(OCR_SERVICE_URL, formData, {
      headers: {
        ...formData.getHeaders(),
        Accept: '*/*',
        'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
        Connection: 'keep-alive',
        'User-Agent':
          'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
      },
      timeout: 120000,
    })

    // Extract JSON data from HTML response
    const htmlContent = response.data
    const jsonMatch = htmlContent.match(/data='(\{.*?\})'|data="(\{.*?\})"/s)
    if (!jsonMatch) {
      throw new Error('Could not find JSON data in OCR response')
    }

    const jsonStr = jsonMatch[1] || jsonMatch[2]
    const parsedData = JSON.parse(jsonStr)

    context.log('OCR processing completed successfully')
    return { structured: { test_results: parsedData.test_results || [] } }
  } catch (error) {
    context.error('OCR processing failed:', error.message)
    throw error
  }
}

function calculateStringSimilarity(str1, str2) {
  const len1 = str1.length
  const len2 = str2.length

  if (len1 === 0) return len2 === 0 ? 1 : 0
  if (len2 === 0) return 0

  const matrix = Array(len1 + 1)
    .fill()
    .map(() => Array(len2 + 1).fill(0))

  for (let i = 0; i <= len1; i++) matrix[i][0] = i
  for (let j = 0; j <= len2; j++) matrix[0][j] = j

  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost,
      )
    }
  }

  const maxLen = Math.max(len1, len2)
  return (maxLen - matrix[len1][len2]) / maxLen
}

async function updateLabTestsWithOCRResults(labTestId, ocrData, context) {
  try {
    context.log('Updating lab tests with OCR results for labTestId:', labTestId)
    context.log('OCR Data received:', JSON.stringify(ocrData, null, 2))

    const labTest = await labTestService.getLabTestById(labTestId)
    if (!labTest || labTest.length === 0) {
      context.error('Lab test not found:', labTestId)
      return false
    }

    const labTestRecord = labTest[0]
    context.log(
      'Lab test record found:',
      JSON.stringify(labTestRecord, null, 2),
    )

    const ocrTestResults = ocrData.structured?.test_results || []
    context.log('OCR test results:', JSON.stringify(ocrTestResults, null, 2))

    if (ocrTestResults.length === 0) {
      context.warn('No OCR test results found in the data')
      return false
    }

    // Create a copy of used OCR results to avoid duplicate matching
    const availableOCRResults = [...ocrTestResults]
    let matchedCount = 0
    let updatedTests = []

    context.log(
      `Starting to process ${labTestRecord.labTests.length} lab tests`,
    )

    for (const test of labTestRecord.labTests) {
      context.log(`\n=== Processing test: "${test.testName}" ===`)

      let matchFound = false
      let matchingOCRTest = null
      let matchingIndex = -1

      // Try to find a matching OCR result
      for (let i = 0; i < availableOCRResults.length; i++) {
        const ocrTest = availableOCRResults[i]

        // Extract OCR test name with multiple field name variations
        let ocrTestName = ''
        if (ocrTest['Test Name']) {
          ocrTestName = ocrTest['Test Name']
        } else if (ocrTest.test_name) {
          ocrTestName = ocrTest.test_name
        } else if (ocrTest.testName) {
          ocrTestName = ocrTest.testName
        } else if (ocrTest.name) {
          ocrTestName = ocrTest.name
        }

        if (!ocrTestName) {
          context.log(`OCR test ${i + 1}: No test name found`)
          continue
        }

        context.log(`OCR test ${i + 1}: "${ocrTestName}"`)

        // Case-insensitive comparison
        const testNameLower = test.testName?.toLowerCase().trim() || ''
        const ocrTestNameLower = ocrTestName.toLowerCase().trim()

        // 1. Exact match (case insensitive)
        if (testNameLower === ocrTestNameLower) {
          context.log(`✓ EXACT MATCH found!`)
          matchFound = true
          matchingOCRTest = ocrTest
          matchingIndex = i
          break
        }

        // 2. Normalize and compare (remove special characters, extra spaces)
        const normalizeString = (str) => {
          return str
            .toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim()
        }

        const normalizedTestName = normalizeString(test.testName || '')
        const normalizedOCRName = normalizeString(ocrTestName)

        if (normalizedTestName === normalizedOCRName) {
          context.log(`✓ NORMALIZED MATCH found!`)
          matchFound = true
          matchingOCRTest = ocrTest
          matchingIndex = i
          break
        }

        // 3. Partial match - check if one contains the other
        if (normalizedTestName.length > 5 && normalizedOCRName.length > 5) {
          if (
            normalizedTestName.includes(normalizedOCRName) ||
            normalizedOCRName.includes(normalizedTestName)
          ) {
            context.log(`✓ PARTIAL MATCH found!`)
            matchFound = true
            matchingOCRTest = ocrTest
            matchingIndex = i
            break
          }
        }

        // 4. Similarity match for longer names
        if (normalizedTestName.length > 8 && normalizedOCRName.length > 8) {
          const similarity = calculateStringSimilarity(
            normalizedTestName,
            normalizedOCRName,
          )
          context.log(`Similarity score: ${similarity.toFixed(2)}`)

          if (similarity >= 0.75) {
            // 75% similarity threshold
            context.log(`✓ SIMILARITY MATCH found! (${similarity.toFixed(2)})`)
            matchFound = true
            matchingOCRTest = ocrTest
            matchingIndex = i
            break
          }
        }
      }

      if (matchFound && matchingOCRTest) {
        context.log(`\n🎯 MATCH CONFIRMED for "${test.testName}"`)
        context.log(
          `Matched with OCR: "${JSON.stringify(matchingOCRTest, null, 2)}"`,
        )

        // Remove the matched OCR result to avoid duplicate matching
        availableOCRResults.splice(matchingIndex, 1)
        matchedCount++

        // Extract result value with multiple field name variations
        const ocrValue =
          matchingOCRTest.result ||
          matchingOCRTest.value ||
          matchingOCRTest.Value ||
          matchingOCRTest.Result ||
          ''

        // Extract reference interval with multiple field name variations
        const ocrReference =
          matchingOCRTest.reference_interval ||
          matchingOCRTest['Reference Interval'] ||
          matchingOCRTest.referenceInterval ||
          matchingOCRTest.reference ||
          matchingOCRTest.Reference ||
          ''

        context.log(`📊 Extracted data:`)
        context.log(`   Result: "${ocrValue}"`)
        context.log(`   Reference: "${ocrReference}"`)

        // Update the test with OCR data and set status to READY
        const updatedTest = {
          ...test,
          results: ocrValue || test.results,
          reference: ocrReference || test.reference,
          status: LabTestStatus.READY,
        }

        updatedTests.push(updatedTest)
        context.log(
          `✅ Test updated successfully - Status: ${updatedTest.status}`,
        )
      } else {
        context.log(`❌ NO MATCH found for "${test.testName}"`)
        // Keep the original test unchanged
        updatedTests.push(test)
      }
    }

    // Update the lab test record with the processed tests
    labTestRecord.labTests = updatedTests

    context.log(`\n📈 FINAL SUMMARY:`)
    context.log(`   Total lab tests: ${labTestRecord.labTests.length}`)
    context.log(`   Successful matches: ${matchedCount}`)
    context.log(`   Tests updated to READY status: ${matchedCount}`)

    if (matchedCount > 0) {
      // Save the updated lab test record to database
      await labTestService.updateLabTest(labTestRecord)

      // Log the final status of each test
      labTestRecord.labTests.forEach((test, index) => {
        context.log(
          `   Test ${index + 1}: "${test.testName}" - Status: ${test.status}`,
        )
      })

      return true
    } else {
      context.log('❌ No matches found - lab test record not updated')
      return false
    }
  } catch (error) {
    context.error('Failed to update lab tests with OCR results:', error.message)
    context.error('Error stack:', error.stack)
    return false
  }
}

app.http('lab-report-upload', {
  methods: ['POST'],
  authLevel: 'function',
  route: 'lab-report/upload',
  handler: async (req, context) => {
    try {
      context.log('Environment check:', {
        hasStorage: !!process.env.AzureWebJobsStorage,
        hasAccountName: !!process.env.AZURE_STORAGE_ACCOUNT_NAME,
        hasAccountKey: !!process.env.AZURE_STORAGE_ACCOUNT_KEY,
        nodeVersion: process.version,
        platform: process.platform,
      })

      const formData = await req.formData()
      const files = formData.getAll('files')
      const patientId = formData.get('patientId')
      const labTestId = formData.get('labTestId')
      const fileIdsToRemove = formData.get('fileIdsToRemove')

      if (!files || files.length === 0 || !patientId || !labTestId) {
        return {
          status: 400,
          jsonBody: { error: 'Missing files, patientId, or labTestId' },
        }
      }

      // Remove old file metadata if provided
      const removedFileIds = []
      if (fileIdsToRemove) {
        try {
          const idsToRemove = JSON.parse(fileIdsToRemove)
          if (Array.isArray(idsToRemove) && idsToRemove.length > 0) {
            context.log(
              `Removing ${idsToRemove.length} old file metadata records`,
            )

            for (const fileId of idsToRemove) {
              try {
                await deleteLabReportMetadata(fileId)
                removedFileIds.push(fileId)
                context.log(`Successfully removed file metadata: ${fileId}`)
              } catch (error) {
                context.log(
                  `Failed to remove file metadata ${fileId}:`,
                  error.message,
                )
              }
            }
          }
        } catch (error) {
          context.log('Error parsing fileIdsToRemove:', error.message)
        }
      }

      const uploadedMetadata = []

      for (const file of files) {
        if (!(file instanceof File) && !(file instanceof Blob)) {
          return {
            status: 400,
            jsonBody: { error: 'Invalid file object received' },
          }
        }

        if (!file.size || file.size === 0) {
          return {
            status: 400,
            jsonBody: { error: 'Empty file not allowed' },
          }
        }
        async function safeArrayBuffer(file, retries = 3) {
          for (let i = 0; i < retries; i++) {
            try {
              context.log(`ArrayBuffer attempt ${i + 1}`)

              const arrayBuffer = await file.arrayBuffer()

              if (!arrayBuffer) {
                throw new Error('ArrayBuffer is null or undefined')
              }

              if (arrayBuffer.byteLength === 0) {
                throw new Error('ArrayBuffer is empty')
              }

              context.log(
                `ArrayBuffer success: ${arrayBuffer.byteLength} bytes`,
              )
              return arrayBuffer
            } catch (error) {
              context.log(`ArrayBuffer attempt ${i + 1} failed:`, error.message)

              if (i === retries - 1) {
                throw new Error(
                  `Failed to read file after ${retries} attempts: ${error.message}`,
                )
              }

              await new Promise((resolve) => setTimeout(resolve, 100 * (i + 1)))
            }
          }
        }

        async function fallbackFileRead(file) {
          context.log('Using fallback file reading method')

          const chunks = []
          const reader = file.stream().getReader()

          try {
            while (true) {
              const { done, value } = await reader.read()
              if (done) break
              chunks.push(value)
            }

            const totalLength = chunks.reduce(
              (acc, chunk) => acc + chunk.length,
              0,
            )
            const buffer = Buffer.concat(
              chunks.map((chunk) => Buffer.from(chunk)),
              totalLength,
            )

            context.log(`Fallback read success: ${buffer.length} bytes`)
            return buffer
          } catch (streamError) {
            throw new Error(
              `Fallback file reading failed: ${streamError.message}`,
            )
          } finally {
            reader.releaseLock()
          }
        }
        let buffer
        try {
          const arrayBuffer = await safeArrayBuffer(file)
          buffer = Buffer.from(arrayBuffer)
        } catch (arrayBufferError) {
          context.log(
            'ArrayBuffer method failed, trying fallback:',
            arrayBufferError.message,
          )
          buffer = await fallbackFileRead(file)
        }

        if (!buffer || buffer.length === 0) {
          return {
            status: 400,
            jsonBody: { error: 'File buffer is empty or invalid' },
          }
        }

        // Encrypt the buffer
        const { encryptedData, encryptionKey, iv } = await encryptBuffer(buffer)
        const fileId = uuidv4()
        const blobName = `patients/${patientId}/labtest/${labTestId}/${fileId}-${file.name}`

        // Azure Blob Storage setup
        const connectionString = process.env.AzureWebJobsStorage || ''
        const containerName = 'lab-reports'

        const blobServiceClient =
          BlobServiceClient.fromConnectionString(connectionString)
        const containerClient =
          blobServiceClient.getContainerClient(containerName)
        await containerClient.createIfNotExists()

        // Generate SAS URL for upload
        const sharedKeyCredential = new StorageSharedKeyCredential(
          process.env.AZURE_STORAGE_ACCOUNT_NAME,
          process.env.AZURE_STORAGE_ACCOUNT_KEY,
        )

        const sasToken = generateBlobSASQueryParameters(
          {
            containerName,
            blobName,
            permissions: BlobSASPermissions.parse('cw'),
            expiresOn: new Date(Date.now() + 10 * 60 * 1000),
          },
          sharedKeyCredential,
        ).toString()

        const uploadUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/${containerName}/${blobName}?${sasToken}`

        await axios.put(uploadUrl, encryptedData, {
          headers: {
            'x-ms-blob-type': 'BlockBlob',
            'Content-Length': encryptedData.length,
          },
          timeout: 30000,
        })

        const metadata = await saveLabReportMetadata({
          id: fileId,
          fileName: file.name,
          fileSize: file.size,
          blobPath: blobName,
          encryptionKey,
          iv,
          patientId,
          labTestId,
          ocrStatus: 'pending',
          detectedLanguage: null,
          ocrData: null,
          fileType: file.type,
          uploadedAt: new Date().toISOString(),
        })

        uploadedMetadata.push(metadata)

        try {
          context.log('Starting OCR processing for file:', file.name)

          await updateLabReportMetadata({
            ...metadata,
            ocrStatus: 'processing',
          })

          const ocrData = await processDocumentWithOCR(
            buffer,
            file.name,
            context,
          )

          await updateLabReportMetadata({
            ...metadata,
            ocrStatus: 'completed',
            ocrData: ocrData,
          })

          context.log(
            'OCR processing completed successfully for file:',
            file.name,
          )
        } catch (ocrError) {
          context.error(
            'OCR processing failed for file:',
            file.name,
            ocrError.message,
          )

          await updateLabReportMetadata({
            ...metadata,
            ocrStatus: 'failed',
            ocrData: null,
          })
        }
      }

      const labTest = await labTestService.getLabTestById(labTestId)
      if (labTest && labTest.length > 0) {
        labTest[0].labTests = labTest[0].labTests.map((test) => {
          return {
            ...test,
            status: LabTestStatus.UPLOADED,
            fileMetadata: uploadedMetadata.filter(
              (meta) => meta.labTestId === test.id,
            ),
          }
        })
        await labTestService.updateLabTest(labTest[0])
      }

      try {
        context.log('Processing OCR results for lab tests')

        const completedOCRFiles = uploadedMetadata.filter(
          (metadata) => metadata.ocrStatus === 'completed',
        )

        context.log(
          `Found ${completedOCRFiles.length} files with completed OCR out of ${uploadedMetadata.length} total files`,
        )

        if (completedOCRFiles.length === 0) {
          context.warn('No files with completed OCR found')
          // Wait a bit and try to process any files that might have completed OCR
          await new Promise((resolve) => setTimeout(resolve, 2000))

          // Re-check for completed OCR files
          const recheckFiles = []
          for (const metadata of uploadedMetadata) {
            try {
              const latestMetadata = await getLabReportMetadata(metadata.id)
              if (
                latestMetadata &&
                latestMetadata.ocrStatus === 'completed' &&
                latestMetadata.ocrData
              ) {
                recheckFiles.push(latestMetadata)
              }
            } catch (error) {
              context.error(
                `Error rechecking metadata for ${metadata.fileName}:`,
                error.message,
              )
            }
          }

          context.log(
            `After recheck, found ${recheckFiles.length} files with completed OCR`,
          )

          if (recheckFiles.length === 0) {
            context.warn(
              'Still no files with completed OCR found after recheck',
            )
            // Continue with the normal flow - don't return early
            // OCR might still be processing
          }

          // Process the rechecked files
          for (const latestMetadata of recheckFiles) {
            try {
              context.log(
                'Processing OCR results for file (recheck):',
                latestMetadata.fileName,
              )

              const updateSuccess = await updateLabTestsWithOCRResults(
                labTestId,
                latestMetadata.ocrData,
                context,
              )

              if (updateSuccess) {
                context.log(
                  'Successfully updated lab tests with OCR results for file:',
                  latestMetadata.fileName,
                )
              } else {
                context.log(
                  'Failed to update lab tests with OCR results for file:',
                  latestMetadata.fileName,
                )
              }
            } catch (ocrProcessError) {
              context.error(
                'Error processing OCR results for file (recheck):',
                latestMetadata.fileName,
                ocrProcessError.message,
              )
            }
          }
        } else {
          // Process files that already have completed OCR
          for (const metadata of completedOCRFiles) {
            try {
              const latestMetadata = await getLabReportMetadata(metadata.id)

              if (
                latestMetadata &&
                latestMetadata.ocrStatus === 'completed' &&
                latestMetadata.ocrData
              ) {
                context.log(
                  'Processing OCR results for file:',
                  latestMetadata.fileName,
                )

                const updateSuccess = await updateLabTestsWithOCRResults(
                  labTestId,
                  latestMetadata.ocrData,
                  context,
                )

                if (updateSuccess) {
                  context.log(
                    'Successfully updated lab tests with OCR results for file:',
                    latestMetadata.fileName,
                  )
                } else {
                  context.log(
                    'Failed to update lab tests with OCR results for file:',
                    latestMetadata.fileName,
                  )
                }
              } else {
                context.log(
                  'OCR not completed or no data available for file:',
                  metadata.fileName,
                )
              }
            } catch (ocrProcessError) {
              context.error(
                'Error processing OCR results for file:',
                metadata.fileName,
                ocrProcessError.message,
              )
            }
          }
        }
      } catch (ocrProcessingError) {
        context.error(
          'Error in OCR results processing:',
          ocrProcessingError.message,
        )
      }

      context.log('Upload completed successfully')

      // Check if any OCR processing was successful and if lab tests were updated
      const completedOCRFiles = uploadedMetadata.filter(
        (meta) => meta.ocrStatus === 'completed',
      )

      let ocrStatus = 'processing'
      let hasMatchedTests = false
      let matchedTestsCount = 0

      if (completedOCRFiles.length > 0) {
        // Check if any lab tests were actually updated to READY status
        const updatedLabTest = await labTestService.getLabTestById(labTestId)
        if (updatedLabTest && updatedLabTest.length > 0) {
          const readyTests = updatedLabTest[0].labTests.filter(
            (test) => test.status === LabTestStatus.READY,
          )
          hasMatchedTests = readyTests.length > 0
          matchedTestsCount = readyTests.length

          if (hasMatchedTests) {
            ocrStatus = 'completed'
            context.log(
              `✅ OCR successful: ${readyTests.length} tests matched and updated`,
            )
          } else {
            ocrStatus = 'no_matches'
            context.log(`❌ OCR completed but no lab tests matched`)
          }
        }
      }

      // If OCR completed but no matches found, return error
      if (completedOCRFiles.length > 0 && !hasMatchedTests) {
        return {
          status: 400,
          jsonBody: {
            error: 'Lab report processed but no matching tests found',
            message:
              'The uploaded lab report was processed successfully, but none of the test names in the report match the lab tests in your order. Please verify that the lab report corresponds to the correct lab test order.',
            uploadedFiles: uploadedMetadata.length,
            labTestId: labTestId,
            ocrStatus: 'no_matches',
            matchedTests: 0,
            metadata: uploadedMetadata.map((meta) => ({
              id: meta.id,
              fileName: meta.fileName,
              fileSize: meta.fileSize,
              fileType: meta.fileType,
              uploadedAt: meta.uploadedAt,
              ocrStatus: meta.ocrStatus,
            })),
            removedFileIds: removedFileIds,
            removedCount: removedFileIds.length,
          },
        }
      }

      return {
        status: 200,
        jsonBody: {
          message: hasMatchedTests
            ? `Upload successful - ${matchedTestsCount} lab tests updated`
            : 'Upload successful - OCR processing in progress',
          uploadedFiles: uploadedMetadata.length,
          labTestId: labTestId,
          ocrStatus: ocrStatus,
          matchedTests: matchedTestsCount,
          metadata: uploadedMetadata.map((meta) => ({
            id: meta.id,
            fileName: meta.fileName,
            fileSize: meta.fileSize,
            fileType: meta.fileType,
            uploadedAt: meta.uploadedAt,
            ocrStatus: meta.ocrStatus,
          })),
          removedFileIds: removedFileIds,
          removedCount: removedFileIds.length,
        },
      }
    } catch (error) {
      context.error('Upload handler error:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
      })

      return {
        status: 500,
        jsonBody: {
          error: 'Internal server error',
          detail: error.message,
          timestamp: new Date().toISOString(),
        },
      }
    }
  },
})

app.http('lab-report-decrypt', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'lab-report/preview',
  handler: async (req, context) => {
    const docId = req.query.get('docId')
    const metadata = await getLabReportMetadata(docId)

    const sharedKeyCredential = new StorageSharedKeyCredential(
      process.env.AZURE_STORAGE_ACCOUNT_NAME,
      process.env.AZURE_STORAGE_ACCOUNT_KEY,
    )

    const sasToken = generateBlobSASQueryParameters(
      {
        containerName: 'lab-reports',
        blobName: metadata.blobPath,
        permissions: BlobSASPermissions.parse('r'),
        expiresOn: new Date(Date.now() + 5 * 60 * 1000),
      },
      sharedKeyCredential,
    ).toString()

    const downloadUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/lab-reports/${metadata.blobPath}?${sasToken}`
    const blobRes = await axios.get(downloadUrl, {
      responseType: 'arraybuffer',
    })

    const decryptedBuffer = await decryptBuffer(
      Buffer.from(blobRes.data),
      metadata.encryptionKey,
      metadata.iv,
    )

    const fileType = await fileTypeFromBuffer(decryptedBuffer)

    return {
      status: 200,
      headers: { 'Content-Type': fileType.mime },
      body: decryptedBuffer,
    }
  },
})

// API endpoint to manually trigger OCR processing for existing files
// app.http('lab-report-ocr-process', {
//   methods: ['POST'],
//   authLevel: 'function',
//   route: 'lab-report/ocr/process',
//   handler: async (req, context) => {
//     try {
//       const { fileId, labTestId } = await req.json()

//       if (!fileId || !labTestId) {
//         return {
//           status: 400,
//           jsonBody: { error: 'Missing fileId or labTestId' },
//         }
//       }

//       // Get file metadata
//       const metadata = await getLabReportMetadata(fileId)
//       if (!metadata) {
//         return {
//           status: 404,
//           jsonBody: { error: 'File not found' },
//         }
//       }

//       // Download and decrypt the file
//       const sharedKeyCredential = new StorageSharedKeyCredential(
//         process.env.AZURE_STORAGE_ACCOUNT_NAME,
//         process.env.AZURE_STORAGE_ACCOUNT_KEY,
//       )

//       const sasToken = generateBlobSASQueryParameters(
//         {
//           containerName: 'lab-reports',
//           blobName: metadata.blobPath,
//           permissions: BlobSASPermissions.parse('r'),
//           expiresOn: new Date(Date.now() + 5 * 60 * 1000),
//         },
//         sharedKeyCredential,
//       ).toString()

//       const downloadUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/lab-reports/${metadata.blobPath}?${sasToken}`
//       const blobRes = await axios.get(downloadUrl, {
//         responseType: 'arraybuffer',
//       })

//       const decryptedBuffer = await decryptBuffer(
//         Buffer.from(blobRes.data),
//         metadata.encryptionKey,
//         metadata.iv,
//       )

//       // Process with OCR
//       try {
//         // Update OCR status to processing
//         await updateLabReportMetadata({
//           ...metadata,
//           ocrStatus: 'processing',
//         })

//         // Send document to OCR service
//         const ocrData = await processDocumentWithOCR(
//           decryptedBuffer,
//           metadata.fileName,
//           context,
//         )

//         // Update metadata with OCR results
//         await updateLabReportMetadata({
//           ...metadata,
//           ocrStatus: 'completed',
//           ocrData: ocrData,
//         })

//         // Update lab tests with OCR results
//         const updateSuccess = await updateLabTestsWithOCRResults(
//           labTestId,
//           ocrData,
//           context,
//         )

//         return {
//           status: 200,
//           jsonBody: {
//             message: 'OCR processing completed successfully',
//             ocrData: ocrData,
//             labTestUpdateSuccess: updateSuccess,
//           },
//         }
//       } catch (ocrError) {
//         context.log.error('OCR processing failed:', ocrError.message)

//         // Update OCR status to failed
//         await updateLabReportMetadata({
//           ...metadata,
//           ocrStatus: 'failed',
//           ocrData: null,
//         })

//         return {
//           status: 500,
//           jsonBody: {
//             error: 'OCR processing failed',
//             detail: ocrError.message,
//           },
//         }
//       }
//     } catch (error) {
//       context.log.error('OCR process handler error:', error.message)
//       return {
//         status: 500,
//         jsonBody: {
//           error: 'Internal server error',
//           detail: error.message,
//         },
//       }
//     }
//   },
// })

// // API endpoint to check OCR status
// app.http('lab-report-ocr-status', {
//   methods: ['GET'],
//   authLevel: 'function',
//   route: 'lab-report/ocr/status',
//   handler: async (req, context) => {
//     try {
//       const fileId = req.query.get('fileId')

//       if (!fileId) {
//         return {
//           status: 400,
//           jsonBody: { error: 'Missing fileId parameter' },
//         }
//       }

//       const metadata = await getLabReportMetadata(fileId)
//       if (!metadata) {
//         return {
//           status: 404,
//           jsonBody: { error: 'File not found' },
//         }
//       }

//       return {
//         status: 200,
//         jsonBody: {
//           fileId: metadata.id,
//           fileName: metadata.fileName,
//           ocrStatus: metadata.ocrStatus,
//           hasOcrData: !!metadata.ocrData,
//           uploadedAt: metadata.uploadedAt,
//           ocrData: metadata.ocrData,
//         },
//       }
//     } catch (error) {
//       context.log.error('OCR status handler error:', error.message)
//       return {
//         status: 500,
//         jsonBody: {
//           error: 'Internal server error',
//           detail: error.message,
//         },
//       }
//     }
//   },
// })

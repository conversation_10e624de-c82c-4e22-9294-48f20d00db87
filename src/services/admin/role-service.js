const roleRepository = require('../../repositories/admin/role-repository')
const { buildRoleAPIs } = require('../../utils/permission-utils')
const rolePermissionService = require('../role-permission-service')

class RoleService {
  async createRole(data) {
    if (data.description && data.description.length > 250) {
      throw new Error('Description must not exceed 250 characters')
    }

    const existingRole = await roleRepository.getRoleByName(
      data.name,
      data.organizationId,
    )
    if (existingRole) {
      throw new Error('Role with the same name already exists')
    }
    const role = await roleRepository.createRole(data)
    const assignedKeys = [
      'emr.lab-test.view',
      'emr.lab-test.manage',
      'emr.lab-test.search',
      'emr.test-package.view',
      'emr.prescription-package.view',
      'role.manage',
      'permission.manage',
      'organization.manage',
      'organization.patients.view',
      'dashboard.view',
      'user.view',
      'user.manage',
      'emr.lifestyle.manage',
      'mrd.patient-queue.manage',
      'emr.patientinfo.edit',
      'emr.doctorprofile.view',
      // 'emr.patientinfo.view.sensitive',
      // 'emr.patientinfo.view.aadhar',
      // 'emr.patientinfo.view.abha',
      'emr.prescription-package.manage',
      'emr.medicine-package.view',
      'emr.consultation.view',
      'emr.consultation.create',
      'emr.consultation.edit',
      'payment.view',
      'emr.patientinfo.view',
      'emr.patientinfo.search',
      'emr.lifestyle.physical-activity.view',
      'mrd.manage-patient.view',
      'mrd.manage-patient.edit',
      'emr.consultation.future.view',
    ]

    const roleAPIs = buildRoleAPIs(assignedKeys)
    const rolePermissionRecord = {
      id: role.id,
      roleName: role.name,
      organizationId: role.organizationId,
      APIs: roleAPIs,
      created_on: new Date().toISOString(),
      updated_on: new Date().toISOString(),
    }

    await rolePermissionService.addRolePermission(rolePermissionRecord)

    return role
  }

  async editRole(data) {
    if (data.description && data.description.length > 250) {
      throw new Error('Description must not exceed 250 characters')
    }
    return roleRepository.updateRole(data)
  }

  async deleteRole(roleId) {
    const role = await roleRepository.getRoleById(roleId)
    if (!role) {
      throw new Error('Role not found')
    }
    if (role.isDefault) {
      throw new Error('Default roles cannot be deleted')
    }

    return roleRepository.deleteRole(roleId)
  }

  async listRoles(organizationId, search) {
    const roles = await roleRepository.getRolesByOrganization(
      organizationId,
      search,
    )
    return roles.map((role) => ({
      id: role.id,
      name: role.name,
      description: role.description || '',
      isDefault: role.isDefault,
      organizationId: role.organizationId,
    }))
  }

  async getRoleById(roleId) {
    return roleRepository.getRoleById(roleId)
  }
}

module.exports = new RoleService()

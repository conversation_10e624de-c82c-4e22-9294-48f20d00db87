const { CosmosClient } = require('@azure/cosmos')
const client = new CosmosClient(process.env.COSMOS_DB_CONNECTIONSTRING)
const databaseId = process.env.COSMOS_DB_DATABASE

const getOrCreateContainer = async (containerId) => {
  await client.databases.createIfNotExists({ id: databaseId })

  await client.database(databaseId).containers.createIfNotExists({
    id: containerId,
    partitionKey: '/id',
    throughput: 400,
  })

  return client.database(databaseId).container(containerId)
}

exports.saveLabReportMetadata = async (
  doc,
  containerName = 'lab_reports-meta-data',
) => {
  const container = await getOrCreateContainer(containerName)
  await container.items.create(doc)
  return doc
}

exports.getLabReportMetadata = async (
  fileId,
  containerName = 'lab_reports-meta-data',
) => {
  const container = await getOrCreateContainer(containerName)
  const { resource } = await container.item(fileId, fileId).read()
  return resource
}

exports.updateLabReportMetadata = async (
  doc,
  containerName = 'lab_reports-meta-data',
) => {
  const container = await getOrCreateContainer(containerName)
  await container.item(doc.id, doc.id).replace(doc)
  return doc
}

exports.deleteLabReportMetadata = async (
  fileId,
  containerName = 'lab_reports-meta-data',
) => {
  const container = await getOrCreateContainer(containerName)
  await container.item(fileId, fileId).delete()
  return { success: true, deletedId: fileId }
}

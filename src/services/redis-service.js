const redis = require("redis");
// Convert Redis client API to use promises, to make it usable with async/await syntax
// Connect to the Azure Cache for Redis over the TLS port using the key.
const cacheConnection = redis.createClient({
    // rediss for TLS
    url: "rediss://" + process.env.REDISCACHEHOSTNAME + ":6380",
    password: process.env.REDISCACHEKEY
});

cacheConnection.connect().then(async () => {
    console.log('Redis connected')
}).catch(async (err) => {
    console.log(`unable to connect redis >>> ${JSON.stringify(err)}`);
});

cacheConnection.on("error", function (error) {
    console.log('Redis disconnected');
});

cacheConnection.on('reconnecting', () => console.log('Redis reconnecting'));
cacheConnection.on('ready', () => console.log('Redis ready'));

// expire time default : 6 hours
const EXPIRED_TIME = 6 * 60 * 60;
const KEY_PREFIX = "ARCAAI-EMR";

class RedisCacheHandler {
    constructor() {

    }
    async set(key, value, expire = EXPIRED_TIME) {
        let cacheValue = JSON.stringify(value);
        let cacheKey = `${KEY_PREFIX}:${key}`;
        if (expire != null) {
            // Set expire
            await cacheConnection.set(cacheKey, cacheValue, 'EX', expire);
        } else {
            await cacheConnection.set(cacheKey, cacheValue);
        }
    }

    async get(key) {
        let cacheKey = `${KEY_PREFIX}:${key}`;
        let cacheValue = await cacheConnection.get(cacheKey);
        return JSON.parse(cacheValue);
    }

    async keys(pattern) {
        let keys = [];
        let prefix = `${KEY_PREFIX}:${pattern}:`;
        let keyPattern = `${KEY_PREFIX}:${pattern}*`;
        let redisKeys = await cacheConnection.keys(keyPattern);
        for (let index = 0; index < redisKeys.length; index++) {
            const key = redisKeys[index];
            keys.push(key.slice(prefix.length));
        }
        return keys;
    }

    async delete(key) {
        let cacheKey = `${KEY_PREFIX}:${key}`;
        return await cacheConnection.del(cacheKey);
    }

    //Clean cache db
    async flush() {
        await cacheConnection.flushAll();
    }
}

module.exports = new RedisCacheHandler();